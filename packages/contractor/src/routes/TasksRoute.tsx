import { TaskList } from '../components'
import type { Task } from '../types'

// Mock data for demonstration
const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Implement user authentication',
    description: 'Set up JWT-based authentication system with login/logout functionality',
    status: 'in_progress',
    priority: 'high',
    projectId: 'project-1',
    contractorId: 'contractor-1',
    estimatedHours: 16,
    actualHours: 12,
    dueDate: new Date('2024-02-15'),
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-02-01')
  },
  {
    id: '2',
    title: 'Design product catalog UI',
    description: 'Create responsive product listing and detail pages',
    status: 'todo',
    priority: 'medium',
    projectId: 'project-1',
    contractorId: 'contractor-1',
    estimatedHours: 24,
    dueDate: new Date('2024-02-20'),
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-01-25')
  },
  {
    id: '3',
    title: 'API integration testing',
    description: 'Write comprehensive tests for all API endpoints',
    status: 'completed',
    priority: 'medium',
    projectId: 'project-2',
    contractorId: 'contractor-1',
    estimatedHours: 8,
    actualHours: 10,
    dueDate: new Date('2024-01-30'),
    completedAt: new Date('2024-01-28'),
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-28')
  }
]

export function TasksRoute() {
  // In a real app, this would fetch data from an API
  // const { data: tasks, isLoading } = useQuery({
  //   queryKey: ['contractor-tasks'],
  //   queryFn: () => fetchContractorTasks()
  // })

  const handleUpdateStatus = (taskId: string, status: Task['status']) => {
    console.log('Update task status:', taskId, status)
    // Update task status via API
  }

  const handleLogTime = (task: Task) => {
    console.log('Log time for task:', task.id)
    // Open time logging modal or form
  }

  return (
    <TaskList
      tasks={mockTasks}
      isLoading={false}
      onUpdateStatus={handleUpdateStatus}
      onLogTime={handleLogTime}
    />
  )
}
