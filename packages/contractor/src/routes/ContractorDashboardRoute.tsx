import { ContractorDashboard } from '../components'
import type { ContractorDashboardStats } from '../types'

// Mock data for demonstration
const mockStats: ContractorDashboardStats = {
  activeProjects: 3,
  completedProjects: 8,
  totalTasks: 25,
  completedTasks: 18,
  totalHours: 320,
  totalEarnings: 24000,
  averageHourlyRate: 75
}

export function ContractorDashboardRoute() {
  // In a real app, this would fetch data from an API
  // const { data: stats, isLoading } = useQuery({
  //   queryKey: ['contractor-dashboard-stats'],
  //   queryFn: () => fetchContractorDashboardStats()
  // })

  return <ContractorDashboard stats={mockStats} isLoading={false} />
}
