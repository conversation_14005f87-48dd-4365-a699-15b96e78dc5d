import { z } from 'zod'

// Task schema
export const TaskSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  status: z.enum(['todo', 'in_progress', 'review', 'completed']),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  projectId: z.string(),
  contractorId: z.string(),
  estimatedHours: z.number().positive(),
  actualHours: z.number().optional(),
  dueDate: z.date(),
  completedAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
})

export type Task = z.infer<typeof TaskSchema>

// Time entry schema
export const TimeEntrySchema = z.object({
  id: z.string(),
  taskId: z.string(),
  contractorId: z.string(),
  description: z.string(),
  hours: z.number().positive(),
  date: z.date(),
  createdAt: z.date(),
  updatedAt: z.date()
})

export type TimeEntry = z.infer<typeof TimeEntrySchema>

// Project assignment schema (contractor's view of a project)
export const ProjectAssignmentSchema = z.object({
  id: z.string(),
  projectId: z.string(),
  projectName: z.string(),
  projectDescription: z.string(),
  ownerName: z.string(),
  status: z.enum(['pending', 'active', 'completed', 'cancelled']),
  hourlyRate: z.number().positive(),
  totalHours: z.number(),
  totalEarnings: z.number(),
  startDate: z.date(),
  endDate: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
})

export type ProjectAssignment = z.infer<typeof ProjectAssignmentSchema>

// Contractor dashboard stats
export const ContractorDashboardStatsSchema = z.object({
  activeProjects: z.number(),
  completedProjects: z.number(),
  totalTasks: z.number(),
  completedTasks: z.number(),
  totalHours: z.number(),
  totalEarnings: z.number(),
  averageHourlyRate: z.number()
})

export type ContractorDashboardStats = z.infer<
  typeof ContractorDashboardStatsSchema
>

// Task creation/update schemas
export const CreateTimeEntrySchema = TimeEntrySchema.omit({
  id: true,
  contractorId: true,
  createdAt: true,
  updatedAt: true
})

export type CreateTimeEntryRequest = z.infer<typeof CreateTimeEntrySchema>

export const UpdateTaskStatusSchema = z.object({
  status: TaskSchema.shape.status,
  actualHours: z.number().positive().optional(),
  completedAt: z.date().optional()
})

export type UpdateTaskStatusRequest = z.infer<typeof UpdateTaskStatusSchema>
