import {
  Card,
  Container,
  Grid,
  Group,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Title,
  IconBriefcase,
  IconClock,
  IconCurrencyDollar,
  IconChecks
} from '@novaest/ui'
import type { ContractorDashboardStats } from '../types'

interface ContractorDashboardProps {
  stats: ContractorDashboardStats
  isLoading?: boolean
}

export function ContractorDashboard({ stats, isLoading = false }: ContractorDashboardProps) {
  const statCards = [
    {
      title: 'Active Projects',
      value: stats.activeProjects,
      icon: IconBriefcase,
      color: 'blue'
    },
    {
      title: 'Completed Tasks',
      value: `${stats.completedTasks}/${stats.totalTasks}`,
      icon: IconChecks,
      color: 'green'
    },
    {
      title: 'Total Hours',
      value: `${stats.totalHours}h`,
      icon: IconClock,
      color: 'violet'
    },
    {
      title: 'Total Earnings',
      value: `$${stats.totalEarnings.toLocaleString()}`,
      icon: IconCurrencyDollar,
      color: 'orange'
    }
  ]

  if (isLoading) {
    return (
      <Container size="xl" py="xl">
        <Stack gap="xl">
          <Title order={1}>Contractor Dashboard</Title>
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
            {Array.from({ length: 4 }).map((_, index) => (
              <Paper key={index} p="md" withBorder>
                <Group justify="space-between">
                  <div>
                    <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                      Loading...
                    </Text>
                    <Text fw={700} size="xl">
                      --
                    </Text>
                  </div>
                </Group>
              </Paper>
            ))}
          </SimpleGrid>
        </Stack>
      </Container>
    )
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="xl">
        <Title order={1}>Contractor Dashboard</Title>
        
        <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
          {statCards.map((stat) => (
            <Paper key={stat.title} p="md" withBorder>
              <Group justify="space-between">
                <div>
                  <Text size="xs" c="dimmed" tt="uppercase" fw={700}>
                    {stat.title}
                  </Text>
                  <Text fw={700} size="xl">
                    {stat.value}
                  </Text>
                </div>
                <stat.icon size={24} color={`var(--mantine-color-${stat.color}-6)`} />
              </Group>
            </Paper>
          ))}
        </SimpleGrid>

        <Grid>
          <Grid.Col span={{ base: 12, md: 8 }}>
            <Card withBorder>
              <Card.Section p="md">
                <Title order={3}>Recent Tasks</Title>
              </Card.Section>
              <Card.Section p="md">
                <Text c="dimmed">
                  Task list will be implemented here
                </Text>
              </Card.Section>
            </Card>
          </Grid.Col>
          
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Card withBorder>
              <Card.Section p="md">
                <Title order={3}>Time Tracking</Title>
              </Card.Section>
              <Card.Section p="md">
                <Text c="dimmed">
                  Time tracking widget will be implemented here
                </Text>
              </Card.Section>
            </Card>
          </Grid.Col>
        </Grid>
      </Stack>
    </Container>
  )
}
