{"name": "@novaest/contractor", "version": "0.0.0", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "tsdown", "dev": "tsdown --watch", "clean": "rm -rf dist", "check-types": "tsc --noEmit"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "dependencies": {"@novaest/shared": "workspace:*", "@novaest/ui": "workspace:*", "@tanstack/react-router": "^1.114.25", "@tanstack/react-query": "^5.80.5", "zod": "^3.25.67"}, "devDependencies": {"@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tsdown": "^0.12.8", "typescript": "^5.8.2"}}