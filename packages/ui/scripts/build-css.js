import { copyFileSync, mkdirSync } from 'node:fs'
import { dirname } from 'node:path'
import { fileURLToPath } from 'node:url'

const __dirname = dirname(fileURLToPath(import.meta.url))

// Ensure dist directory exists
mkdirSync(`${__dirname}/../dist`, { recursive: true })

// Copy styles.css to dist
copyFileSync(
  `${__dirname}/../src/styles.css`,
  `${__dirname}/../dist/styles.css`
)

console.log('✓ CSS files copied to dist')
