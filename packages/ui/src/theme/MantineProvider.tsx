import {
  createTheme,
  type Mantine<PERSON>olo<PERSON>Tuple,
  Mantine<PERSON>rovider as Provider
} from '@mantine/core'
import { DatesProvider } from '@mantine/dates'
import { ModalsProvider } from '@mantine/modals'
import { Notifications } from '@mantine/notifications'
import type { ReactNode } from 'react'

// Custom colors for your brand
const primaryColor: MantineColorsTuple = [
  '#f0f9ff',
  '#e0f2fe',
  '#bae6fd',
  '#7dd3fc',
  '#38bdf8',
  '#0ea5e9',
  '#0284c7',
  '#0369a1',
  '#075985',
  '#0c4a6e'
]

const theme = createTheme({
  colors: {
    primary: primaryColor
  },
  primaryColor: 'primary',
  fontFamily:
    'Inter, Geist, system-ui, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
  defaultRadius: 'md',
  respectReducedMotion: true,
  cursorType: 'pointer',
  components: {
    Button: {
      defaultProps: {
        variant: 'filled'
      }
    }
  }
})

interface MantineProviderProps {
  children: ReactNode
}

export function MantineProvider({ children }: MantineProviderProps) {
  return (
    <Provider theme={theme} defaultColorScheme="dark">
      <DatesProvider settings={{ locale: 'vi', firstDayOfWeek: 1 }}>
        <ModalsProvider>
          <Notifications position="top-right" zIndex={2077} />
          {children}
        </ModalsProvider>
      </DatesProvider>
    </Provider>
  )
}
