{"name": "@novaest/ui", "version": "0.0.0", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./styles": {"import": "./dist/styles.css"}}, "files": ["dist"], "scripts": {"build": "tsdown && node scripts/build-css.js", "dev": "tsdown --watch --onSuccess \"node scripts/build-css.js\"", "dev:ui": "tsdown --watch --onSuccess \"node scripts/build-css.js\"", "clean": "rm -rf dist", "check-types": "tsc --noEmit"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "dependencies": {"@mantine/carousel": "^7.17.2", "@mantine/charts": "^7.17.2", "@mantine/core": "^7.17.2", "@mantine/dates": "^7.17.2", "@mantine/dropzone": "^7.17.2", "@mantine/form": "^7.17.2", "@mantine/hooks": "^7.17.2", "@mantine/modals": "^7.17.2", "@mantine/notifications": "^7.17.2", "@mantine/spotlight": "^7.17.2", "@mantine/tiptap": "^7.17.2", "@tabler/icons-react": "^3.31.0", "clsx": "^2.1.1"}, "devDependencies": {"@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "react": "^19.0.0", "react-dom": "^19.0.0", "tsdown": "^0.12.8", "typescript": "^5.8.2"}}