{"name": "@novaest/ui", "version": "0.0.0", "type": "module", "main": "./src/index.ts", "module": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "types": "./src/index.ts"}, "./styles": {"import": "./dist/styles.css"}}, "scripts": {"check-types": "tsc --noEmit"}, "peerDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}, "dependencies": {"@mantine/carousel": "^7.17.2", "@mantine/charts": "^7.17.2", "@mantine/core": "^7.17.2", "@mantine/dates": "^7.17.2", "@mantine/dropzone": "^7.17.2", "@mantine/form": "^7.17.2", "@mantine/hooks": "^7.17.2", "@mantine/modals": "^7.17.2", "@mantine/notifications": "^7.17.2", "@mantine/spotlight": "^7.17.2", "@mantine/tiptap": "^7.17.2", "@tabler/icons-react": "^3.31.0", "clsx": "^2.1.1"}, "devDependencies": {"@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.8.2"}}