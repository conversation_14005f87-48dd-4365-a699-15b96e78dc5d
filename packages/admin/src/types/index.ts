import { z } from 'zod'
import { UserSchema } from '@novaest/shared'

// System metrics schema
export const SystemMetricsSchema = z.object({
  totalUsers: z.number(),
  activeUsers: z.number(),
  totalProjects: z.number(),
  activeProjects: z.number(),
  totalRevenue: z.number(),
  systemUptime: z.number(),
  averageResponseTime: z.number(),
  errorRate: z.number()
})

export type SystemMetrics = z.infer<typeof SystemMetricsSchema>

// User management schemas
export const UserManagementSchema = UserSchema.extend({
  lastLoginAt: z.date().optional(),
  isActive: z.boolean(),
  projectCount: z.number(),
  totalEarnings: z.number().optional()
})

export type UserManagement = z.infer<typeof UserManagementSchema>

export const CreateUserSchema = z.object({
  email: z.string().email(),
  name: z.string().min(2),
  role: z.enum(['owner', 'contractor', 'admin']),
  password: z.string().min(6),
  isActive: z.boolean().default(true)
})

export type CreateUserRequest = z.infer<typeof CreateUserSchema>

export const UpdateUserSchema = CreateUserSchema.partial().omit({ password: true })

export type UpdateUserRequest = z.infer<typeof UpdateUserSchema>

// System settings schema
export const SystemSettingsSchema = z.object({
  id: z.string(),
  siteName: z.string(),
  siteDescription: z.string(),
  maintenanceMode: z.boolean(),
  allowRegistration: z.boolean(),
  defaultUserRole: z.enum(['owner', 'contractor']),
  maxProjectsPerOwner: z.number(),
  maxContractorsPerProject: z.number(),
  emailNotifications: z.boolean(),
  systemTimezone: z.string(),
  updatedAt: z.date(),
  updatedBy: z.string()
})

export type SystemSettings = z.infer<typeof SystemSettingsSchema>

export const UpdateSystemSettingsSchema = SystemSettingsSchema.omit({
  id: true,
  updatedAt: true,
  updatedBy: true
})

export type UpdateSystemSettingsRequest = z.infer<typeof UpdateSystemSettingsSchema>

// Audit log schema
export const AuditLogSchema = z.object({
  id: z.string(),
  userId: z.string(),
  userName: z.string(),
  action: z.string(),
  resource: z.string(),
  resourceId: z.string().optional(),
  details: z.record(z.unknown()).optional(),
  ipAddress: z.string(),
  userAgent: z.string(),
  createdAt: z.date()
})

export type AuditLog = z.infer<typeof AuditLogSchema>

// Admin dashboard stats
export const AdminDashboardStatsSchema = z.object({
  systemMetrics: SystemMetricsSchema,
  userStats: z.object({
    totalOwners: z.number(),
    totalContractors: z.number(),
    totalAdmins: z.number(),
    newUsersThisMonth: z.number()
  }),
  projectStats: z.object({
    totalProjects: z.number(),
    activeProjects: z.number(),
    completedProjects: z.number(),
    averageProjectValue: z.number()
  }),
  recentActivity: z.array(AuditLogSchema).max(10)
})

export type AdminDashboardStats = z.infer<typeof AdminDashboardStatsSchema>
