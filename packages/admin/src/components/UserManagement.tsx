import {
  <PERSON>ge,
  <PERSON><PERSON>,
  Card,
  Container,
  Group,
  Stack,
  Text,
  Title,
  Table,
  ActionIcon,
  IconEdit,
  IconTrash,
  IconUserPlus
} from '@novaest/ui'
import { formatDateTime, getRoleDisplayName } from '@novaest/shared'
import type { UserManagement } from '../types'

interface UserManagementProps {
  users: UserManagement[]
  isLoading?: boolean
  onEdit?: (user: UserManagement) => void
  onDelete?: (userId: string) => void
  onCreateUser?: () => void
}

export function UserManagement({
  users,
  isLoading = false,
  onEdit,
  onDelete,
  onCreateUser
}: UserManagementProps) {
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'blue'
      case 'contractor':
        return 'green'
      case 'admin':
        return 'violet'
      default:
        return 'gray'
    }
  }

  if (isLoading) {
    return (
      <Container size="xl" py="xl">
        <Stack gap="md">
          <Group justify="space-between">
            <Title order={2}>User Management</Title>
            <Button leftSection={<IconUserPlus size={16} />}>Add User</Button>
          </Group>
          <Card withBorder>
            <Text>Loading users...</Text>
          </Card>
        </Stack>
      </Container>
    )
  }

  return (
    <Container size="xl" py="xl">
      <Stack gap="md">
        <Group justify="space-between">
          <Title order={2}>User Management</Title>
          {onCreateUser && (
            <Button
              leftSection={<IconUserPlus size={16} />}
              onClick={onCreateUser}
            >
              Add User
            </Button>
          )}
        </Group>

        <Card withBorder>
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Name</Table.Th>
                <Table.Th>Email</Table.Th>
                <Table.Th>Role</Table.Th>
                <Table.Th>Status</Table.Th>
                <Table.Th>Projects</Table.Th>
                <Table.Th>Last Login</Table.Th>
                <Table.Th>Actions</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {users.map((user) => (
                <Table.Tr key={user.id}>
                  <Table.Td>
                    <Text fw={500}>{user.name}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm" c="dimmed">
                      {user.email}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge color={getRoleColor(user.role)} variant="light">
                      {getRoleDisplayName(user.role)}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      color={user.isActive ? 'green' : 'red'}
                      variant="light"
                    >
                      {user.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{user.projectCount}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm" c="dimmed">
                      {user.lastLoginAt
                        ? formatDateTime(user.lastLoginAt)
                        : 'Never'}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Group gap="xs">
                      {onEdit && (
                        <ActionIcon
                          variant="subtle"
                          color="blue"
                          onClick={() => onEdit(user)}
                        >
                          <IconEdit size={16} />
                        </ActionIcon>
                      )}
                      {onDelete && user.role !== 'admin' && (
                        <ActionIcon
                          variant="subtle"
                          color="red"
                          onClick={() => onDelete(user.id)}
                        >
                          <IconTrash size={16} />
                        </ActionIcon>
                      )}
                    </Group>
                  </Table.Td>
                </Table.Tr>
              ))}
            </Table.Tbody>
          </Table>

          {users.length === 0 && (
            <Text c="dimmed" ta="center" py="xl">
              No users found.
            </Text>
          )}
        </Card>
      </Stack>
    </Container>
  )
}
