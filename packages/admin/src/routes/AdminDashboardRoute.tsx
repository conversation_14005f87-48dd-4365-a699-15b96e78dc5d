import { AdminDashboard } from '../components'
import type { AdminDashboardStats } from '../types'

// Mock data for demonstration
const mockStats: AdminDashboardStats = {
  systemMetrics: {
    totalUsers: 156,
    activeUsers: 89,
    totalProjects: 45,
    activeProjects: 23,
    totalRevenue: 485000,
    systemUptime: 99.8,
    averageResponseTime: 245,
    errorRate: 0.02
  },
  userStats: {
    totalOwners: 45,
    totalContractors: 98,
    totalAdmins: 13,
    newUsersThisMonth: 24
  },
  projectStats: {
    totalProjects: 45,
    activeProjects: 23,
    completedProjects: 22,
    averageProjectValue: 10778
  },
  recentActivity: [
    {
      id: '1',
      userId: 'user-1',
      userName: '<PERSON>',
      action: 'created',
      resource: 'project',
      resourceId: 'project-123',
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0...',
      createdAt: new Date('2024-01-25T10:30:00Z')
    },
    {
      id: '2',
      userId: 'user-2',
      userName: '<PERSON>',
      action: 'updated',
      resource: 'user profile',
      resourceId: 'user-456',
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0...',
      createdAt: new Date('2024-01-25T09:15:00Z')
    },
    {
      id: '3',
      userId: 'user-3',
      userName: 'Mike Johnson',
      action: 'completed',
      resource: 'task',
      resourceId: 'task-789',
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0...',
      createdAt: new Date('2024-01-25T08:45:00Z')
    }
  ]
}

export function AdminDashboardRoute() {
  // In a real app, this would fetch data from an API
  // const { data: stats, isLoading } = useQuery({
  //   queryKey: ['admin-dashboard-stats'],
  //   queryFn: () => fetchAdminDashboardStats()
  // })

  return <AdminDashboard stats={mockStats} isLoading={false} />
}
