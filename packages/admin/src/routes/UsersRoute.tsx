import { UserManagement } from '../components'
import type { UserManagement as UserManagementType } from '../types'

// Mock data for demonstration
const mockUsers: UserManagementType[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'owner',
    avatar: undefined,
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2024-01-20'),
    lastLoginAt: new Date('2024-01-25T08:30:00Z'),
    isActive: true,
    projectCount: 5,
    totalEarnings: undefined
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'contractor',
    avatar: undefined,
    createdAt: new Date('2023-11-15'),
    updatedAt: new Date('2024-01-22'),
    lastLoginAt: new Date('2024-01-24T14:20:00Z'),
    isActive: true,
    projectCount: 8,
    totalEarnings: 45000
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'contractor',
    avatar: undefined,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-15'),
    lastLoginAt: new Date('2024-01-23T16:45:00Z'),
    isActive: true,
    projectCount: 2,
    totalEarnings: 12000
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: 'Sarah Wilson',
    role: 'owner',
    avatar: undefined,
    createdAt: new Date('2023-10-20'),
    updatedAt: new Date('2024-01-18'),
    lastLoginAt: undefined,
    isActive: false,
    projectCount: 0,
    totalEarnings: undefined
  },
  {
    id: '5',
    email: '<EMAIL>',
    name: 'System Admin',
    role: 'admin',
    avatar: undefined,
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-01-25'),
    lastLoginAt: new Date('2024-01-25T10:00:00Z'),
    isActive: true,
    projectCount: 0,
    totalEarnings: undefined
  }
]

export function UsersRoute() {
  // In a real app, this would fetch data from an API
  // const { data: users, isLoading } = useQuery({
  //   queryKey: ['admin-users'],
  //   queryFn: () => fetchUsers()
  // })

  const handleEdit = (user: UserManagementType) => {
    console.log('Edit user:', user.id)
    // Navigate to edit form or open modal
  }

  const handleDelete = (userId: string) => {
    console.log('Delete user:', userId)
    // Show confirmation dialog and delete
  }

  const handleCreateUser = () => {
    console.log('Create new user')
    // Navigate to create form or open modal
  }

  return (
    <UserManagement
      users={mockUsers}
      isLoading={false}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onCreateUser={handleCreateUser}
    />
  )
}
