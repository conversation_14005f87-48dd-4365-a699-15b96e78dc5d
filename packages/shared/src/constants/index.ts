// Application constants
export const APP_NAME = 'Novaest'
export const APP_DESCRIPTION = 'Modern role-based application platform'

// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile'
  },
  USERS: {
    LIST: '/users',
    CREATE: '/users',
    UPDATE: (id: string) => `/users/${id}`,
    DELETE: (id: string) => `/users/${id}`,
    GET: (id: string) => `/users/${id}`
  }
} as const

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'novaest_auth_token',
  REFRESH_TOKEN: 'novaest_refresh_token',
  USER_PREFERENCES: 'novaest_user_preferences',
  THEME: 'novaest_theme'
} as const

// Route paths
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  PROFILE: '/profile',
  OWNER: {
    ROOT: '/owner',
    DASHBOARD: '/owner/dashboard',
    PROJECTS: '/owner/projects',
    CONTRACTORS: '/owner/contractors',
    SETTINGS: '/owner/settings'
  },
  CONTRACTOR: {
    ROOT: '/contractor',
    DASHBOARD: '/contractor/dashboard',
    PROJECTS: '/contractor/projects',
    PROFILE: '/contractor/profile',
    SETTINGS: '/contractor/settings'
  },
  ADMIN: {
    ROOT: '/admin',
    DASHBOARD: '/admin/dashboard',
    USERS: '/admin/users',
    SYSTEM: '/admin/system',
    SETTINGS: '/admin/settings'
  }
} as const

// Validation constants
export const VALIDATION = {
  PASSWORD_MIN_LENGTH: 6,
  NAME_MIN_LENGTH: 2,
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE_REGEX: /^\+?[\d\s-()]+$/
} as const
