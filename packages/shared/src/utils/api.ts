import type { ApiResponse, QueryParams } from '../types'

/**
 * Create query string from parameters
 */
export function createQueryString(params: Partial<QueryParams>): string {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, String(value))
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

/**
 * Generic API error class
 */
export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public errors?: string[]
  ) {
    super(message)
    this.name = 'ApiError'
  }
}

/**
 * Handle API response and throw errors if needed
 */
export async function handleApiResponse<T>(
  response: Response
): Promise<ApiResponse<T>> {
  const data = await response.json()
  
  if (!response.ok) {
    throw new ApiError(
      data.message || 'An error occurred',
      response.status,
      data.errors
    )
  }
  
  return data
}

/**
 * Create API client with base configuration
 */
export function createApiClient(baseUrl: string, defaultHeaders: Record<string, string> = {}) {
  return {
    async get<T>(endpoint: string, headers: Record<string, string> = {}): Promise<ApiResponse<T>> {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'GET',
        headers: { ...defaultHeaders, ...headers }
      })
      return handleApiResponse<T>(response)
    },
    
    async post<T>(
      endpoint: string, 
      body?: unknown, 
      headers: Record<string, string> = {}
    ): Promise<ApiResponse<T>> {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...defaultHeaders,
          ...headers
        },
        body: body ? JSON.stringify(body) : undefined
      })
      return handleApiResponse<T>(response)
    },
    
    async put<T>(
      endpoint: string, 
      body?: unknown, 
      headers: Record<string, string> = {}
    ): Promise<ApiResponse<T>> {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...defaultHeaders,
          ...headers
        },
        body: body ? JSON.stringify(body) : undefined
      })
      return handleApiResponse<T>(response)
    },
    
    async delete<T>(endpoint: string, headers: Record<string, string> = {}): Promise<ApiResponse<T>> {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers: { ...defaultHeaders, ...headers }
      })
      return handleApiResponse<T>(response)
    }
  }
}
