import type { User, UserRole } from '../types'

/**
 * Check if user has a specific role
 */
export function hasRole(user: User | null, role: UserRole): boolean {
  return user?.role === role
}

/**
 * Check if user has any of the specified roles
 */
export function hasAnyRole(user: User | null, roles: UserRole[]): boolean {
  return user ? roles.includes(user.role) : false
}

/**
 * Check if user has all of the specified roles (for future multi-role support)
 */
export function hasAllRoles(user: User | null, roles: UserRole[]): boolean {
  if (!user) return false
  // For now, since users have single roles, this checks if user's role is in the array
  return roles.includes(user.role)
}

/**
 * Get user display name
 */
export function getUserDisplayName(user: User | null): string {
  if (!user) return 'Guest'
  return user.name || user.email
}

/**
 * Get role display name
 */
export function getRoleDisplayName(role: UserRole): string {
  const roleNames = {
    owner: 'Owner',
    contractor: 'Contractor',
    admin: 'Administrator'
  }
  return roleNames[role] || role
}

/**
 * Check if user can access a route based on role requirements
 */
export function canAccessRoute(
  user: User | null,
  allowedRoles?: UserRole[]
): boolean {
  if (!allowedRoles || allowedRoles.length === 0) {
    return true // No role restrictions
  }

  return hasAnyRole(user, allowedRoles)
}
