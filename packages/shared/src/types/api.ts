import { z } from 'zod'

// Generic API response wrapper
export const ApiResponseSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    success: z.boolean(),
    data: dataSchema.optional(),
    message: z.string().optional(),
    errors: z.array(z.string()).optional()
  })

export type ApiResponse<T> = {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
}

// Pagination schema
export const PaginationSchema = z.object({
  page: z.number().min(1),
  limit: z.number().min(1).max(100),
  total: z.number(),
  totalPages: z.number()
})

export type Pagination = z.infer<typeof PaginationSchema>

// Paginated response
export const PaginatedResponseSchema = <T extends z.ZodTypeAny>(
  itemSchema: T
) =>
  z.object({
    items: z.array(itemSchema),
    pagination: PaginationSchema
  })

export type PaginatedResponse<T> = {
  items: T[]
  pagination: Pagination
}

// Common query parameters
export const QueryParamsSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc')
})

export type QueryParams = z.infer<typeof QueryParamsSchema>
