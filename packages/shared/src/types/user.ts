import { z } from 'zod'

// User role enum
export const UserRole = {
  OWNER: 'owner',
  CONTRACTOR: 'contractor',
  ADMIN: 'admin'
} as const

export type UserRole = typeof UserRole[keyof typeof UserRole]

// User schema
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  name: z.string(),
  role: z.enum([UserRole.OWNER, UserRole.CONTRACTOR, UserRole.ADMIN]),
  avatar: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date()
})

export type User = z.infer<typeof UserSchema>

// Authentication schemas
export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6)
})

export type LoginRequest = z.infer<typeof LoginSchema>

export const RegisterSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  name: z.string().min(2),
  role: z.enum([UserRole.OWNER, UserRole.CONTRACTOR])
})

export type RegisterRequest = z.infer<typeof RegisterSchema>

// Auth context type
export interface AuthContextType {
  user: User | null
  isLoading: boolean
  login: (credentials: LoginRequest) => Promise<void>
  register: (data: RegisterRequest) => Promise<void>
  logout: () => Promise<void>
  hasRole: (role: UserRole) => boolean
  hasAnyRole: (roles: UserRole[]) => boolean
}
