import { OwnerDashboard } from '../components'
import type { OwnerDashboardStats } from '../types'

// Mock data for demonstration
const mockStats: OwnerDashboardStats = {
  totalProjects: 12,
  activeProjects: 5,
  completedProjects: 7,
  totalBudget: 150000,
  totalContractors: 8,
  averageProjectDuration: 45
}

export function OwnerDashboardRoute() {
  // In a real app, this would fetch data from an API
  // const { data: stats, isLoading } = useQuery({
  //   queryKey: ['owner-dashboard-stats'],
  //   queryFn: () => fetchOwnerDashboardStats()
  // })

  return <OwnerDashboard stats={mockStats} isLoading={false} />
}
