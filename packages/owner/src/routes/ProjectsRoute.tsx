import { ProjectList } from '../components'
import type { Project } from '../types'

// Mock data for demonstration
const mockProjects: Project[] = [
  {
    id: '1',
    name: 'E-commerce Website',
    description: 'Build a modern e-commerce platform with React and Node.js',
    status: 'active',
    budget: 25000,
    startDate: new Date('2024-01-15'),
    endDate: new Date('2024-03-15'),
    ownerId: 'owner-1',
    contractorIds: ['contractor-1', 'contractor-2'],
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: '2',
    name: 'Mobile App Development',
    description: 'Cross-platform mobile app using React Native',
    status: 'completed',
    budget: 35000,
    startDate: new Date('2023-10-01'),
    endDate: new Date('2023-12-31'),
    ownerId: 'owner-1',
    contractorIds: ['contractor-3'],
    createdAt: new Date('2023-09-25'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: '3',
    name: 'Data Analytics Dashboard',
    description: 'Business intelligence dashboard with real-time analytics',
    status: 'draft',
    budget: 18000,
    startDate: new Date('2024-02-01'),
    ownerId: 'owner-1',
    contractorIds: [],
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-01-25')
  }
]

export function ProjectsRoute() {
  // In a real app, this would fetch data from an API
  // const { data: projects, isLoading } = useQuery({
  //   queryKey: ['owner-projects'],
  //   queryFn: () => fetchOwnerProjects()
  // })

  const handleEdit = (project: Project) => {
    console.log('Edit project:', project.id)
    // Navigate to edit form or open modal
  }

  const handleDelete = (projectId: string) => {
    console.log('Delete project:', projectId)
    // Show confirmation dialog and delete
  }

  const handleView = (project: Project) => {
    console.log('View project:', project.id)
    // Navigate to project details
  }

  return (
    <ProjectList
      projects={mockProjects}
      isLoading={false}
      onEdit={handleEdit}
      onDelete={handleDelete}
      onView={handleView}
    />
  )
}
