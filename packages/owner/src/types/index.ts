import { z } from 'zod'

// Project schema
export const ProjectSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  status: z.enum(['draft', 'active', 'completed', 'cancelled']),
  budget: z.number().positive(),
  startDate: z.date(),
  endDate: z.date().optional(),
  ownerId: z.string(),
  contractorIds: z.array(z.string()),
  createdAt: z.date(),
  updatedAt: z.date()
})

export type Project = z.infer<typeof ProjectSchema>

// Contractor schema
export const ContractorSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string().optional(),
  skills: z.array(z.string()),
  hourlyRate: z.number().positive(),
  rating: z.number().min(0).max(5),
  totalProjects: z.number(),
  isAvailable: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date()
})

export type Contractor = z.infer<typeof ContractorSchema>

// Project creation/update schemas
export const CreateProjectSchema = ProjectSchema.omit({
  id: true,
  ownerId: true,
  createdAt: true,
  updatedAt: true
})

export type CreateProjectRequest = z.infer<typeof CreateProjectSchema>

export const UpdateProjectSchema = CreateProjectSchema.partial()

export type UpdateProjectRequest = z.infer<typeof UpdateProjectSchema>

// Dashboard stats
export const OwnerDashboardStatsSchema = z.object({
  totalProjects: z.number(),
  activeProjects: z.number(),
  completedProjects: z.number(),
  totalBudget: z.number(),
  totalContractors: z.number(),
  averageProjectDuration: z.number()
})

export type OwnerDashboardStats = z.infer<typeof OwnerDashboardStatsSchema>
