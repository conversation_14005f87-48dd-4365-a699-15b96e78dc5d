import path from 'node:path'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [TanStackRouterVite({}), react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@novaest/ui': path.resolve(__dirname, '../../packages/ui/src'),
      '@novaest/ui/styles.css': path.resolve(
        __dirname,
        '../../packages/ui/src/styles.css'
      ),
      '@novaest/shared': path.resolve(__dirname, '../../packages/shared/src'),
      '@novaest/owner': path.resolve(__dirname, '../../packages/owner/src'),
      '@novaest/contractor': path.resolve(
        __dirname,
        '../../packages/contractor/src'
      ),
      '@novaest/admin': path.resolve(__dirname, '../../packages/admin/src')
    }
  }
})
