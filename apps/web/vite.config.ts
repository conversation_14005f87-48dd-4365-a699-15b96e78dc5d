import path from 'node:path'
import { tanstackRouter } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react'
import { visualizer } from 'rollup-plugin-visualizer'
import { defineConfig } from 'vite'

export default defineConfig(({ mode }) => ({
  plugins: [
    tanstackRouter({}),
    react({
      // Enable React Fast Refresh for better HMR
      fastRefresh: true
    }),
    // Bundle analyzer for production builds
    ...(mode === 'analyze'
      ? [
          visualizer({
            filename: 'dist/bundle-analysis.html',
            open: true,
            gzipSize: true,
            brotliSize: true
          })
        ]
      : [])
  ],

  resolve: {
    alias: {
      // Main app alias
      '@': path.resolve(__dirname, './src'),

      // Package aliases - always use source for optimal tree shaking
      '@novaest/shared': path.resolve(__dirname, '../../packages/shared/src'),
      '@novaest/ui': path.resolve(__dirname, '../../packages/ui/src'),
      '@novaest/owner': path.resolve(__dirname, '../../packages/owner/src'),
      '@novaest/contractor': path.resolve(
        __dirname,
        '../../packages/contractor/src'
      ),
      '@novaest/admin': path.resolve(__dirname, '../../packages/admin/src'),

      // Direct CSS import for UI package
      '@novaest/ui/styles.css': path.resolve(
        __dirname,
        '../../packages/ui/src/styles.css'
      )
    }
  },

  // Optimize dependency pre-bundling
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@tanstack/react-router',
      '@tanstack/react-query',
      '@mantine/core',
      '@mantine/hooks'
    ],
    // Force re-bundle on package changes
    force: process.env.NODE_ENV === 'development'
  },
  build: {
    // Optimize build performance
    target: 'esnext',
    minify: 'esbuild',

    rollupOptions: {
      output: {
        // Optimized chunking strategy for role-based architecture
        manualChunks: (id) => {
          // Vendor chunks - group by library for better caching
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor-react'
            }
            if (id.includes('@tanstack')) {
              return 'vendor-tanstack'
            }
            if (id.includes('@mantine')) {
              return 'vendor-mantine'
            }
            if (id.includes('zod')) {
              return 'vendor-validation'
            }
            return 'vendor-misc'
          }

          // Internal package chunks - role-based splitting for lazy loading
          if (id.includes('packages/owner/src')) {
            return 'role-owner'
          }
          if (id.includes('packages/contractor/src')) {
            return 'role-contractor'
          }
          if (id.includes('packages/admin/src')) {
            return 'role-admin'
          }
          if (
            id.includes('packages/shared/src') ||
            id.includes('packages/ui/src')
          ) {
            return 'shared-foundation'
          }
        },

        // Optimize chunk naming for better caching
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
          if (facadeModuleId && facadeModuleId.includes('packages/')) {
            return 'chunks/[name]-[hash].js'
          }
          return 'chunks/[name]-[hash].js'
        }
      },

      // Tree shaking optimization
      treeshake: {
        moduleSideEffects: false,
        propertyReadSideEffects: false,
        unknownGlobalSideEffects: false
      }
    },

    // Performance settings
    chunkSizeWarningLimit: 600,
    sourcemap: process.env.NODE_ENV === 'development',

    // Enable CSS code splitting
    cssCodeSplit: true,

    // Optimize asset handling
    assetsInlineLimit: 4096
  }
}))
