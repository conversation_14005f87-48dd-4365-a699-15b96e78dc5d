import path from 'node:path'
import { tanstackRouter } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

export default defineConfig(({ mode }) => {
  const isDev = mode === 'development'

  // In development, use source directly for faster HMR
  // In production, use built packages for better optimization
  const getPackagePath = (packageName: string) => {
    if (isDev) {
      return path.resolve(__dirname, `../../packages/${packageName}/src`)
    }
    return path.resolve(__dirname, `../../packages/${packageName}/dist`)
  }

  return {
    plugins: [tanstackRouter({}), react()],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '@novaest/ui': getPackagePath('ui'),
        '@novaest/ui/styles.css': path.resolve(
          __dirname,
          '../../packages/ui/src/styles.css'
        ),
        '@novaest/shared': getPackagePath('shared'),
        '@novaest/owner': getPackagePath('owner'),
        '@novaest/contractor': getPackagePath('contractor'),
        '@novaest/admin': getPackagePath('admin')
      }
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Vendor chunks
            if (id.includes('node_modules')) {
              if (id.includes('react') || id.includes('react-dom')) {
                return 'vendor-react'
              }
              if (id.includes('@tanstack')) {
                return 'vendor-tanstack'
              }
              if (id.includes('@mantine')) {
                return 'vendor-mantine'
              }
              return 'vendor'
            }

            // Role-based chunks
            if (id.includes('@novaest/owner')) {
              return 'role-owner'
            }
            if (id.includes('@novaest/contractor')) {
              return 'role-contractor'
            }
            if (id.includes('@novaest/admin')) {
              return 'role-admin'
            }
            if (id.includes('@novaest/shared') || id.includes('@novaest/ui')) {
              return 'shared'
            }
          }
        }
      },
      chunkSizeWarningLimit: 600,
      sourcemap: isDev
    }
  }
})
