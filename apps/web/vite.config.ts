import path from 'node:path'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [TanStackRouterVite({}), react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@novaest/ui': path.resolve(__dirname, '../../packages/ui/src'),
      '@novaest/ui/styles.css': path.resolve(
        __dirname,
        '../../packages/ui/src/styles.css'
      ),
      '@novaest/shared': path.resolve(__dirname, '../../packages/shared/src'),
      '@novaest/owner': path.resolve(__dirname, '../../packages/owner/src'),
      '@novaest/contractor': path.resolve(
        __dirname,
        '../../packages/contractor/src'
      ),
      '@novaest/admin': path.resolve(__dirname, '../../packages/admin/src')
    }
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom')) {
              return 'vendor-react'
            }
            if (id.includes('@tanstack')) {
              return 'vendor-tanstack'
            }
            if (id.includes('@mantine')) {
              return 'vendor-mantine'
            }
            return 'vendor'
          }

          // Role-based chunks
          if (id.includes('@novaest/owner')) {
            return 'role-owner'
          }
          if (id.includes('@novaest/contractor')) {
            return 'role-contractor'
          }
          if (id.includes('@novaest/admin')) {
            return 'role-admin'
          }
          if (id.includes('@novaest/shared') || id.includes('@novaest/ui')) {
            return 'shared'
          }
        }
      }
    },
    chunkSizeWarningLimit: 600,
    sourcemap: process.env.NODE_ENV === 'development'
  }
})
