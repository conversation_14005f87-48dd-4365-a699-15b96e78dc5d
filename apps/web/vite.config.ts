import path from 'node:path'
import { TanStackRouterVite } from '@tanstack/router-plugin/vite'
import react from '@vitejs/plugin-react'
import { defineConfig } from 'vite'

export default defineConfig({
  plugins: [TanStackRouterVite({}), react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@novaest/ui': path.resolve(__dirname, '../../packages/ui/src'),
      '@novaest/ui/styles.css': path.resolve(
        __dirname,
        '../../packages/ui/src/styles.css'
      )
    }
  }
})
