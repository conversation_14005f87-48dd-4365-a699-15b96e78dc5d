{"name": "web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --port=3001", "format": "biome format src --write", "lint": "biome check src", "lint:fix": "biome check src --write", "build": "vite build", "serve": "vite preview", "start": "vite", "check-types": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@novaest/admin": "workspace:*", "@novaest/contractor": "workspace:*", "@novaest/owner": "workspace:*", "@novaest/shared": "workspace:*", "@novaest/ui": "workspace:*", "@tanstack/react-form": "^1.12.3", "@tanstack/react-query": "^5.80.5", "@tanstack/react-router": "^1.114.25", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "zod": "^3.25.67"}, "devDependencies": {"@tanstack/react-query-devtools": "^5.80.5", "@tanstack/react-router-devtools": "^1.114.27", "@tanstack/router-plugin": "^1.114.27", "@types/node": "^22.13.13", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "rollup-plugin-visualizer": "^6.0.3", "typescript": "^5.8.2", "vite": "^6.2.2"}}