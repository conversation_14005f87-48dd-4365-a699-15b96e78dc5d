import { createFileRoute, redirect } from '@tanstack/react-router'
import { ContractorDashboardRoute } from '@novaest/contractor'

export const Route = createFileRoute('/contractor')({
  beforeLoad: ({ context }) => {
    // In a real app, you would check authentication here
    // For now, we'll assume the user is authenticated and has the right role
    // if (!context.auth?.user || context.auth.user.role !== 'contractor') {
    //   throw redirect({ to: '/login' })
    // }
  },
  component: ContractorDashboardRoute
})
