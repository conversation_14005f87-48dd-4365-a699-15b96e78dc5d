import type { RegisterRequest } from '@novaest/shared'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Container,
  IconAlertCircle,
  Paper,
  PasswordInput,
  Select,
  Stack,
  Text,
  TextInput,
  Title
} from '@novaest/ui'
import { createFileRoute, Link } from '@tanstack/react-router'
import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'

export const Route = createFileRoute('/register')({
  component: RegisterPage
})

function RegisterPage() {
  const [formData, setFormData] = useState<RegisterRequest>({
    email: '',
    password: '',
    name: '',
    role: 'owner'
  })
  const [error, setError] = useState<string | null>(null)
  const { register, isLoading } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    try {
      await register(formData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Registration failed')
    }
  }

  const handleChange =
    (field: keyof RegisterRequest) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: e.target.value
      }))
    }

  const handleRoleChange = (value: string | null) => {
    if (value && (value === 'owner' || value === 'contractor')) {
      setFormData((prev) => ({
        ...prev,
        role: value
      }))
    }
  }

  return (
    <Container size="xs" py="xl">
      <Paper p="xl" radius="md" withBorder>
        <Stack gap="lg">
          <Title order={2} ta="center">
            Create Account
          </Title>

          <Text size="sm" c="dimmed" ta="center">
            Join Novaest to start managing projects
          </Text>

          {error && (
            <Alert icon={<IconAlertCircle size={16} />} color="red">
              {error}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <Stack gap="md">
              <TextInput
                label="Full Name"
                placeholder="John Doe"
                value={formData.name}
                onChange={handleChange('name')}
                required
              />

              <TextInput
                label="Email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleChange('email')}
                required
                type="email"
              />

              <PasswordInput
                label="Password"
                placeholder="Choose a strong password"
                value={formData.password}
                onChange={handleChange('password')}
                required
              />

              <Select
                label="Account Type"
                placeholder="Select your role"
                value={formData.role}
                onChange={handleRoleChange}
                data={[
                  {
                    value: 'owner',
                    label: 'Project Owner - I want to hire contractors'
                  },
                  {
                    value: 'contractor',
                    label: 'Contractor - I want to work on projects'
                  }
                ]}
                required
              />

              <Button
                type="submit"
                fullWidth
                loading={isLoading}
                disabled={
                  !formData.email || !formData.password || !formData.name
                }
              >
                Create Account
              </Button>
            </Stack>
          </form>

          <Text size="sm" ta="center">
            Already have an account?{' '}
            <Link
              search={{ redirect: '/', error: 'Please sign in to continue' }}
              to="/login"
            >
              <Text component="span" c="blue" style={{ cursor: 'pointer' }}>
                Sign in
              </Text>
            </Link>
          </Text>
        </Stack>
      </Paper>
    </Container>
  )
}
