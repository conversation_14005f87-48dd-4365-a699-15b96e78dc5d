import { useState } from 'react'
import {
  createFileRout<PERSON>,
  Link,
  useNavigate,
  useSearch
} from '@tanstack/react-router'
import {
  Container,
  Paper,
  Title,
  TextInput,
  PasswordInput,
  Button,
  Stack,
  Text,
  Alert,
  IconAlertCircle
} from '@novaest/ui'
import { useAuth } from '@/contexts/AuthContext'
import type { LoginRequest } from '@novaest/shared'

export const Route = createFileRoute('/login')({
  validateSearch: (search: Record<string, unknown>) => {
    return {
      redirect: (search.redirect as string) || undefined,
      error: (search.error as string) || undefined
    }
  },
  component: LoginPage
})

function LoginPage() {
  const [formData, setFormData] = useState<LoginRequest>({
    email: '',
    password: ''
  })
  const [error, setError] = useState<string | null>(null)
  const { login, isLoading } = useAuth()
  const navigate = useNavigate()
  const search = useSearch({ from: '/login' })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    try {
      await login(formData, search.redirect)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed')
    }
  }

  const handleChange =
    (field: keyof LoginRequest) => (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: e.target.value
      }))
    }

  return (
    <Container size="xs" py="xl">
      <Paper p="xl" radius="md" withBorder>
        <Stack gap="lg">
          <Title order={2} ta="center">
            Welcome Back
          </Title>

          <Text size="sm" c="dimmed" ta="center">
            Sign in to your account to continue
          </Text>

          {(error || search.error) && (
            <Alert icon={<IconAlertCircle size={16} />} color="red">
              {error || search.error}
            </Alert>
          )}

          {search.redirect && (
            <Alert color="blue">
              Please sign in to access {search.redirect}
            </Alert>
          )}

          <form onSubmit={handleSubmit}>
            <Stack gap="md">
              <TextInput
                label="Email"
                placeholder="<EMAIL>"
                value={formData.email}
                onChange={handleChange('email')}
                required
                type="email"
              />

              <PasswordInput
                label="Password"
                placeholder="Your password"
                value={formData.password}
                onChange={handleChange('password')}
                required
              />

              <Button
                type="submit"
                fullWidth
                loading={isLoading}
                disabled={!formData.email || !formData.password}
              >
                Sign In
              </Button>
            </Stack>
          </form>

          <Text size="sm" ta="center">
            Don't have an account?{' '}
            <Link to="/register">
              <Text component="span" c="blue" style={{ cursor: 'pointer' }}>
                Sign up
              </Text>
            </Link>
          </Text>

          <Stack gap="xs">
            <Text size="xs" c="dimmed" ta="center">
              Demo accounts (password: password123):
            </Text>
            <Text size="xs" c="dimmed" ta="center">
              Owner: <EMAIL>
            </Text>
            <Text size="xs" c="dimmed" ta="center">
              Contractor: <EMAIL>
            </Text>
            <Text size="xs" c="dimmed" ta="center">
              Admin: <EMAIL>
            </Text>
          </Stack>
        </Stack>
      </Paper>
    </Container>
  )
}
