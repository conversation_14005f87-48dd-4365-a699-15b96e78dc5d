import { createFileRoute, redirect } from '@tanstack/react-router'
import { OwnerDashboardRoute } from '@novaest/owner'

export const Route = createFileRoute('/owner')({
  beforeLoad: ({ context }) => {
    // In a real app, you would check authentication here
    // For now, we'll assume the user is authenticated and has the right role
    // if (!context.auth?.user || context.auth.user.role !== 'owner') {
    //   throw redirect({ to: '/login' })
    // }
  },
  component: OwnerDashboardRoute
})
