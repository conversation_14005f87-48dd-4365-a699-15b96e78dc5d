import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import {
  createRootRouteWithContext,
  HeadContent,
  Outlet,
  useRouterState
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import Header from '@/components/header'
import Loader from '@/components/loader'
import ErrorBoundary from '@/components/ErrorBoundary'
import { AuthProvider, useAuth } from '@/contexts/AuthContext'
import '../index.css'
import { MantineProvider } from '@novaest/ui'
import type { RouterContext } from '@/main'

const queryClient = new QueryClient()

export type RouterAppContext = RouterContext

export const Route = createRootRouteWithContext<RouterAppContext>()({
  component: RootComponent,
  head: () => ({
    meta: [
      {
        title: 'Novaest - Binance Alpha Points Tracker'
      },
      {
        name: 'description',
        content:
          'Track your Binance Alpha Points and optimize your trading strategy'
      }
    ],
    links: [
      {
        rel: 'icon',
        href: '/favicon.ico'
      }
    ]
  })
})

function RootComponent() {
  const isFetching = useRouterState({
    select: (s) => s.isLoading
  })

  return (
    <>
      <HeadContent />
      <ErrorBoundary>
        <QueryClientProvider client={queryClient}>
          <MantineProvider>
            <AuthProvider>
              <AuthContextProvider />
            </AuthProvider>
          </MantineProvider>
          <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
      </ErrorBoundary>
      <TanStackRouterDevtools position="bottom-left" />
    </>
  )
}

function AuthContextProvider() {
  const auth = useAuth()
  const isFetching = useRouterState({
    select: (s) => s.isLoading
  })

  // Show loading screen while auth is initializing
  if (auth.isLoading) {
    return <Loader />
  }

  return (
    <div
      style={{
        height: '100vh',
        display: 'grid',
        gridTemplateRows: 'auto 1fr'
      }}
    >
      <Header />
      {isFetching ? <Loader /> : <Outlet context={{ auth }} />}
    </div>
  )
}
