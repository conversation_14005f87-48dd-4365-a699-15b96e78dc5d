import {
  createRootRouteWithContext,
  HeadContent,
  Outlet,
  useRouterState
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/react-router-devtools'
import Header from '@/components/header'
import Loader from '@/components/loader'
import '../index.css'
import { MantineProvider } from '@novaest/ui'

export type RouterAppContext = Record<string, unknown>

export const Route = createRootRouteWithContext<RouterAppContext>()({
  component: RootComponent,
  head: () => ({
    meta: [
      {
        title: 'Novaest - Binance Alpha Points Tracker'
      },
      {
        name: 'description',
        content:
          'Track your Binance Alpha Points and optimize your trading strategy'
      }
    ],
    links: [
      {
        rel: 'icon',
        href: '/favicon.ico'
      }
    ]
  })
})

function RootComponent() {
  const isFetching = useRouterState({
    select: (s) => s.isLoading
  })

  return (
    <>
      <HeadContent />
      <MantineProvider>
        <div
          style={{
            height: '100vh',
            display: 'grid',
            gridTemplateRows: 'auto 1fr'
          }}
        >
          <Header />
          {isFetching ? <Loader /> : <Outlet />}
        </div>
      </MantineProvider>
      <TanStackRouterDevtools position="bottom-left" />
    </>
  )
}
