import { createFileRoute, redirect } from '@tanstack/react-router'
import { AdminDashboardRoute } from '@novaest/admin'

export const Route = createFileRoute('/admin')({
  beforeLoad: ({ context }) => {
    // In a real app, you would check authentication here
    // For now, we'll assume the user is authenticated and has the right role
    // if (!context.auth?.user || context.auth.user.role !== 'admin') {
    //   throw redirect({ to: '/login' })
    // }
  },
  component: AdminDashboardRoute
})
