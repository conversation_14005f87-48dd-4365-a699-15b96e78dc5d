import {
  Button,
  Center,
  Container,
  Group,
  Paper,
  Stack,
  Text,
  Title
} from '@novaest/ui'
import { createFileRoute, Link, useNavigate } from '@tanstack/react-router'
import { useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'

export const Route = createFileRoute('/')({
  component: HomePage
})

function HomePage() {
  const { user } = useAuth()
  const navigate = useNavigate()

  // Redirect authenticated users to their dashboard
  useEffect(() => {
    if (user) {
      switch (user.role) {
        case 'owner':
          navigate({ to: '/owner' })
          break
        case 'contractor':
          navigate({ to: '/contractor' })
          break
        case 'admin':
          navigate({ to: '/admin' })
          break
      }
    }
  }, [user, navigate])

  // Show landing page for unauthenticated users
  if (!user) {
    return (
      <Container size="lg" py="xl">
        <Center style={{ minHeight: '60vh' }}>
          <Paper
            p="xl"
            radius="md"
            withBorder
            style={{ maxWidth: 600, width: '100%' }}
          >
            <Stack gap="lg" align="center">
              <Title order={1} size="h2" ta="center">
                Welcome to Novaest
              </Title>

              <Text size="lg" c="dimmed" ta="center">
                The modern platform for project owners, contractors, and
                administrators to collaborate efficiently.
              </Text>

              <Text size="md" ta="center">
                Streamline your project management with role-based access,
                real-time collaboration, and powerful analytics.
              </Text>

              <Group gap="md" mt="lg">
                <Button component={Link} to="/register" size="lg">
                  Get Started
                </Button>
                <Button
                  component={Link}
                  to="/login"
                  variant="outline"
                  size="lg"
                >
                  Sign In
                </Button>
              </Group>

              <Stack gap="xs" mt="xl">
                <Text size="sm" c="dimmed" ta="center">
                  ✨ For Project Owners: Manage projects and hire contractors
                </Text>
                <Text size="sm" c="dimmed" ta="center">
                  🔧 For Contractors: Find work and track your progress
                </Text>
                <Text size="sm" c="dimmed" ta="center">
                  ⚙️ For Administrators: Oversee the entire platform
                </Text>
              </Stack>
            </Stack>
          </Paper>
        </Center>
      </Container>
    )
  }

  // This will be replaced by the redirect effect above
  return null
}
