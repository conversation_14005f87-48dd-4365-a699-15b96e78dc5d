/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as RegisterRouteImport } from './routes/register'
import { Route as OwnerRouteImport } from './routes/owner'
import { Route as LoginRouteImport } from './routes/login'
import { Route as ContractorRouteImport } from './routes/contractor'
import { Route as AdminRouteImport } from './routes/admin'
import { Route as IndexRouteImport } from './routes/index'
import { Route as OwnerProjectsRouteImport } from './routes/owner.projects'
import { Route as ContractorTasksRouteImport } from './routes/contractor.tasks'
import { Route as AdminUsersRouteImport } from './routes/admin.users'

const RegisterRoute = RegisterRouteImport.update({
  id: '/register',
  path: '/register',
  getParentRoute: () => rootRouteImport,
} as any)
const OwnerRoute = OwnerRouteImport.update({
  id: '/owner',
  path: '/owner',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const ContractorRoute = ContractorRouteImport.update({
  id: '/contractor',
  path: '/contractor',
  getParentRoute: () => rootRouteImport,
} as any)
const AdminRoute = AdminRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const OwnerProjectsRoute = OwnerProjectsRouteImport.update({
  id: '/projects',
  path: '/projects',
  getParentRoute: () => OwnerRoute,
} as any)
const ContractorTasksRoute = ContractorTasksRouteImport.update({
  id: '/tasks',
  path: '/tasks',
  getParentRoute: () => ContractorRoute,
} as any)
const AdminUsersRoute = AdminUsersRouteImport.update({
  id: '/users',
  path: '/users',
  getParentRoute: () => AdminRoute,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/contractor': typeof ContractorRouteWithChildren
  '/login': typeof LoginRoute
  '/owner': typeof OwnerRouteWithChildren
  '/register': typeof RegisterRoute
  '/admin/users': typeof AdminUsersRoute
  '/contractor/tasks': typeof ContractorTasksRoute
  '/owner/projects': typeof OwnerProjectsRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/contractor': typeof ContractorRouteWithChildren
  '/login': typeof LoginRoute
  '/owner': typeof OwnerRouteWithChildren
  '/register': typeof RegisterRoute
  '/admin/users': typeof AdminUsersRoute
  '/contractor/tasks': typeof ContractorTasksRoute
  '/owner/projects': typeof OwnerProjectsRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/admin': typeof AdminRouteWithChildren
  '/contractor': typeof ContractorRouteWithChildren
  '/login': typeof LoginRoute
  '/owner': typeof OwnerRouteWithChildren
  '/register': typeof RegisterRoute
  '/admin/users': typeof AdminUsersRoute
  '/contractor/tasks': typeof ContractorTasksRoute
  '/owner/projects': typeof OwnerProjectsRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/admin'
    | '/contractor'
    | '/login'
    | '/owner'
    | '/register'
    | '/admin/users'
    | '/contractor/tasks'
    | '/owner/projects'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/admin'
    | '/contractor'
    | '/login'
    | '/owner'
    | '/register'
    | '/admin/users'
    | '/contractor/tasks'
    | '/owner/projects'
  id:
    | '__root__'
    | '/'
    | '/admin'
    | '/contractor'
    | '/login'
    | '/owner'
    | '/register'
    | '/admin/users'
    | '/contractor/tasks'
    | '/owner/projects'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AdminRoute: typeof AdminRouteWithChildren
  ContractorRoute: typeof ContractorRouteWithChildren
  LoginRoute: typeof LoginRoute
  OwnerRoute: typeof OwnerRouteWithChildren
  RegisterRoute: typeof RegisterRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/register': {
      id: '/register'
      path: '/register'
      fullPath: '/register'
      preLoaderRoute: typeof RegisterRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/owner': {
      id: '/owner'
      path: '/owner'
      fullPath: '/owner'
      preLoaderRoute: typeof OwnerRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/contractor': {
      id: '/contractor'
      path: '/contractor'
      fullPath: '/contractor'
      preLoaderRoute: typeof ContractorRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/admin': {
      id: '/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AdminRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/owner/projects': {
      id: '/owner/projects'
      path: '/projects'
      fullPath: '/owner/projects'
      preLoaderRoute: typeof OwnerProjectsRouteImport
      parentRoute: typeof OwnerRoute
    }
    '/contractor/tasks': {
      id: '/contractor/tasks'
      path: '/tasks'
      fullPath: '/contractor/tasks'
      preLoaderRoute: typeof ContractorTasksRouteImport
      parentRoute: typeof ContractorRoute
    }
    '/admin/users': {
      id: '/admin/users'
      path: '/users'
      fullPath: '/admin/users'
      preLoaderRoute: typeof AdminUsersRouteImport
      parentRoute: typeof AdminRoute
    }
  }
}

interface AdminRouteChildren {
  AdminUsersRoute: typeof AdminUsersRoute
}

const AdminRouteChildren: AdminRouteChildren = {
  AdminUsersRoute: AdminUsersRoute,
}

const AdminRouteWithChildren = AdminRoute._addFileChildren(AdminRouteChildren)

interface ContractorRouteChildren {
  ContractorTasksRoute: typeof ContractorTasksRoute
}

const ContractorRouteChildren: ContractorRouteChildren = {
  ContractorTasksRoute: ContractorTasksRoute,
}

const ContractorRouteWithChildren = ContractorRoute._addFileChildren(
  ContractorRouteChildren,
)

interface OwnerRouteChildren {
  OwnerProjectsRoute: typeof OwnerProjectsRoute
}

const OwnerRouteChildren: OwnerRouteChildren = {
  OwnerProjectsRoute: OwnerProjectsRoute,
}

const OwnerRouteWithChildren = OwnerRoute._addFileChildren(OwnerRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AdminRoute: AdminRouteWithChildren,
  ContractorRoute: ContractorRouteWithChildren,
  LoginRoute: LoginRoute,
  OwnerRoute: OwnerRouteWithChildren,
  RegisterRoute: RegisterRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
