import type {
  AuthContextType,
  LoginRequest,
  RegisterRequest,
  User
} from '@novaest/shared'
import { hasAnyRole, hasRole, STORAGE_KEYS } from '@novaest/shared'
import { useNavigate } from '@tanstack/react-router'
import {
  createContext,
  type ReactNode,
  useContext,
  useEffect,
  useState
} from 'react'

const AuthContext = createContext<AuthContextType | null>(null)

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const navigate = useNavigate()

  // Mock users for demonstration
  const mockUsers: User[] = [
    {
      id: '1',
      email: '<EMAIL>',
      name: 'John Owner',
      role: 'owner',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: '2',
      email: '<EMAIL>',
      name: '<PERSON> Contractor',
      role: 'contractor',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    },
    {
      id: '3',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
      createdAt: new Date('2024-01-01'),
      updatedAt: new Date('2024-01-01')
    }
  ]

  // Check for existing session on mount
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const token = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)
        const userData = localStorage.getItem('user_data')

        if (token && userData) {
          const parsedUser = JSON.parse(userData)
          // Convert date strings back to Date objects
          parsedUser.createdAt = new Date(parsedUser.createdAt)
          parsedUser.updatedAt = new Date(parsedUser.updatedAt)
          setUser(parsedUser)
        }
      } catch (error) {
        console.error('Auth check failed:', error)
        localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN)
        localStorage.removeItem('user_data')
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [])

  const login = async (credentials: LoginRequest, redirectTo?: string) => {
    setIsLoading(true)
    try {
      // Mock login - find user by email
      const foundUser = mockUsers.find((u) => u.email === credentials.email)

      if (!foundUser) {
        throw new Error('Invalid email or password')
      }

      // Mock password validation (in real app, this would be handled by backend)
      if (credentials.password !== 'password123') {
        throw new Error('Invalid email or password')
      }

      // Store auth data
      const mockToken = `mock_token_${foundUser.id}_${Date.now()}`
      localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, mockToken)
      localStorage.setItem('user_data', JSON.stringify(foundUser))

      setUser(foundUser)

      // Navigate to redirect URL or default based on role
      if (redirectTo) {
        navigate({ to: redirectTo })
      } else {
        switch (foundUser.role) {
          case 'owner':
            navigate({ to: '/owner' })
            break
          case 'contractor':
            navigate({ to: '/contractor' })
            break
          case 'admin':
            navigate({ to: '/admin' })
            break
          default:
            navigate({ to: '/admin' })
        }
      }
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (data: RegisterRequest) => {
    setIsLoading(true)
    try {
      // Mock registration - check if email already exists
      const existingUser = mockUsers.find((u) => u.email === data.email)

      if (existingUser) {
        throw new Error('Email already registered')
      }

      // Create new user
      const newUser: User = {
        id: `user_${Date.now()}`,
        email: data.email,
        name: data.name,
        role: data.role,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      // Store auth data
      const mockToken = `mock_token_${newUser.id}_${Date.now()}`
      localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, mockToken)
      localStorage.setItem('user_data', JSON.stringify(newUser))

      setUser(newUser)

      // Navigate based on role
      switch (newUser.role) {
        case 'owner':
          navigate({ to: '/owner' })
          break
        case 'contractor':
          navigate({ to: '/contractor' })
          break
        default:
          navigate({ to: '/admin' })
      }
    } catch (error) {
      console.error('Registration failed:', error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async () => {
    setIsLoading(true)
    try {
      localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN)
      localStorage.removeItem('user_data')
      setUser(null)
      navigate({
        to: '/login',
        search: { error: 'You have been logged out', redirect: '/' }
      })
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const contextValue: AuthContextType = {
    user,
    isLoading,
    login,
    register,
    logout,
    hasRole: (role) => hasRole(user, role),
    hasAnyRole: (roles) => hasAnyRole(user, roles)
  }

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  )
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
