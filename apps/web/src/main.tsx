import type { AuthContextType } from '@novaest/shared'
import { createRouter, RouterProvider } from '@tanstack/react-router'
import ReactDOM from 'react-dom/client'
import Loader from './components/loader'
import { routeTree } from './routeTree.gen'

// Import integration test utilities for development
if (import.meta.env.DEV) {
  import('./utils/integration-test').then(() => {
    console.log('🔧 Development mode: Integration test utilities loaded')
    console.log('Run integrationTest.runAll() in console to test integration')
  })
}

export interface RouterContext {
  auth: AuthContextType | undefined
}

const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  defaultPendingComponent: () => <Loader />,
  context: {
    auth: undefined
  } as RouterContext
})

declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router
  }
}

const rootElement = document.getElementById('app')

if (!rootElement) {
  throw new Error('Root element not found')
}

if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement)
  root.render(<RouterProvider router={router} />)
}
