import {
  ActionIcon,
  Avatar,
  Button,
  Group,
  IconLogout,
  IconMoon,
  IconSun,
  IconUser,
  Menu,
  rem,
  Text,
  useMantineColorScheme
} from '@novaest/ui'
import { Link } from '@tanstack/react-router'
import { useAuth } from '@/contexts/AuthContext'

export default function Header() {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme()
  const { user, logout } = useAuth()

  const handleLogout = async () => {
    try {
      await logout()
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'owner':
        return 'Project Owner'
      case 'contractor':
        return 'Contractor'
      case 'admin':
        return 'Administrator'
      default:
        return role
    }
  }

  const getRoleDashboardPath = (role: string) => {
    switch (role) {
      case 'owner':
        return '/owner'
      case 'contractor':
        return '/contractor'
      case 'admin':
        return '/admin'
      default:
        return '/'
    }
  }

  return (
    <Group
      h={60}
      px="md"
      justify="space-between"
      style={{ borderBottom: '1px solid var(--mantine-color-default-border)' }}
    >
      <Link to={user ? getRoleDashboardPath(user.role) : '/'}>
        <Button variant="subtle" size="md">
          Novaest
        </Button>
      </Link>

      <Group gap="sm">
        <ActionIcon
          onClick={() => toggleColorScheme()}
          variant="default"
          size="lg"
          aria-label="Toggle color scheme"
        >
          {colorScheme === 'dark' ? (
            <IconSun style={{ width: rem(20), height: rem(20) }} />
          ) : (
            <IconMoon style={{ width: rem(20), height: rem(20) }} />
          )}
        </ActionIcon>

        {user ? (
          <Menu shadow="md" width={200}>
            <Menu.Target>
              <Button variant="subtle" leftSection={<Avatar size="sm" />}>
                {user.name}
              </Button>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Label>
                <Text size="xs" c="dimmed">
                  {getRoleDisplayName(user.role)}
                </Text>
              </Menu.Label>

              <Menu.Item
                leftSection={
                  <IconUser style={{ width: rem(14), height: rem(14) }} />
                }
                component={Link}
                to={getRoleDashboardPath(user.role)}
              >
                Dashboard
              </Menu.Item>

              <Menu.Divider />

              <Menu.Item
                color="red"
                leftSection={
                  <IconLogout style={{ width: rem(14), height: rem(14) }} />
                }
                onClick={handleLogout}
              >
                Logout
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        ) : (
          <Group gap="xs">
            <Button component={Link} to="/login" variant="subtle">
              Sign In
            </Button>
            <Button component={Link} to="/register">
              Sign Up
            </Button>
          </Group>
        )}
      </Group>
    </Group>
  )
}
