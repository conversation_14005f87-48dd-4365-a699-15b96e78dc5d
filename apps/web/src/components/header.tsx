import {
  ActionIcon,
  Button,
  Group,
  IconMoon,
  IconSun,
  rem,
  useMantineColorScheme
} from '@novaest/ui'
import { Link } from '@tanstack/react-router'

export default function Header() {
  const { colorScheme, toggleColorScheme } = useMantineColorScheme()

  return (
    <Group
      h={60}
      px="md"
      justify="space-between"
      style={{ borderBottom: '1px solid var(--mantine-color-default-border)' }}
    >
      <Link to="/">
        <Button variant="subtle" size="md">
          Novaest
        </Button>
      </Link>

      <ActionIcon
        onClick={() => toggleColorScheme()}
        variant="default"
        size="lg"
        aria-label="Toggle color scheme"
      >
        {colorScheme === 'dark' ? (
          <IconSun style={{ width: rem(20), height: rem(20) }} />
        ) : (
          <IconMoon style={{ width: rem(20), height: rem(20) }} />
        )}
      </ActionIcon>
    </Group>
  )
}
