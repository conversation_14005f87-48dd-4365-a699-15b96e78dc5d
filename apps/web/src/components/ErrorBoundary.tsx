import React from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON>, Container, Paper, Stack, Text, Title, IconAlertTriangle } from '@novaest/ui'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
  errorInfo?: React.ErrorInfo
}

interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return {
      hasError: true,
      error
    }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.setState({
      error,
      errorInfo
    })
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback
        return <FallbackComponent error={this.state.error!} retry={this.handleRetry} />
      }

      return <DefaultErrorFallback error={this.state.error!} retry={this.handleRetry} />
    }

    return this.props.children
  }
}

interface ErrorFallbackProps {
  error: Error
  retry: () => void
}

function DefaultErrorFallback({ error, retry }: ErrorFallbackProps) {
  const isDevelopment = import.meta.env.DEV

  return (
    <Container size="md" py="xl">
      <Paper p="xl" radius="md" withBorder>
        <Stack gap="lg" align="center">
          <IconAlertTriangle size={48} color="red" />
          
          <Title order={2} ta="center" c="red">
            Something went wrong
          </Title>
          
          <Text size="lg" c="dimmed" ta="center">
            An unexpected error occurred while loading the application.
          </Text>

          <Alert color="red" style={{ width: '100%' }}>
            <Text size="sm" fw={500}>
              Error: {error.message}
            </Text>
          </Alert>

          {isDevelopment && (
            <Alert color="yellow" style={{ width: '100%' }}>
              <Text size="xs" ff="monospace">
                <strong>Stack trace (development only):</strong>
                <br />
                {error.stack}
              </Text>
            </Alert>
          )}

          <Stack gap="md" align="center">
            <Button onClick={retry} size="lg">
              Try Again
            </Button>
            
            <Button 
              variant="outline" 
              onClick={() => window.location.reload()}
              size="md"
            >
              Reload Page
            </Button>
          </Stack>

          <Stack gap="xs" align="center">
            <Text size="sm" c="dimmed">
              If this problem persists, please try:
            </Text>
            <Text size="xs" c="dimmed">
              • Clearing your browser cache
            </Text>
            <Text size="xs" c="dimmed">
              • Checking your internet connection
            </Text>
            <Text size="xs" c="dimmed">
              • Contacting support if the issue continues
            </Text>
          </Stack>
        </Stack>
      </Paper>
    </Container>
  )
}

// Package-specific error boundary for role-based components
export function PackageErrorBoundary({ 
  children, 
  packageName 
}: { 
  children: React.ReactNode
  packageName: string 
}) {
  const PackageFallback = ({ error, retry }: ErrorFallbackProps) => (
    <Container size="md" py="xl">
      <Paper p="xl" radius="md" withBorder>
        <Stack gap="lg" align="center">
          <IconAlertTriangle size={32} color="orange" />
          
          <Title order={3} ta="center" c="orange">
            Package Error
          </Title>
          
          <Text size="md" c="dimmed" ta="center">
            Failed to load the {packageName} package.
          </Text>

          <Alert color="orange" style={{ width: '100%' }}>
            <Text size="sm">
              <strong>Error:</strong> {error.message}
            </Text>
            <Text size="xs" mt="xs">
              This might be due to a package integration issue or missing dependencies.
            </Text>
          </Alert>

          <Stack gap="md" align="center">
            <Button onClick={retry} variant="outline">
              Retry Loading
            </Button>
            
            <Button 
              variant="subtle" 
              onClick={() => window.location.href = '/'}
            >
              Go to Home
            </Button>
          </Stack>
        </Stack>
      </Paper>
    </Container>
  )

  return (
    <ErrorBoundary fallback={PackageFallback}>
      {children}
    </ErrorBoundary>
  )
}

export default ErrorBoundary
