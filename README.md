# 🏗️ Novaest - Modular Role-Based Architecture

A modern, scalable web application built with **clean architecture principles** and **role-based modular design**. This project demonstrates enterprise-grade patterns with TypeScript, React 19, and a comprehensive monorepo structure.

## 🌟 Architecture Highlights

- **🎯 Role-Based Modules**: Separate packages for Owner, Contractor, and Admin roles
- **🔒 Authentication Guards**: Route-level protection with TanStack Router
- **📦 Monorepo Structure**: Optimized with pnpm workspaces and Turborepo
- **🧩 Clean Architecture**: Separation of concerns with shared utilities
- **⚡ Performance Optimized**: Code splitting, caching, and dependency management
- **🛡️ Type Safety**: Full TypeScript coverage with strict typing

## 🚀 Key Features

- **TypeScript** - Strict type safety across all packages
- **React 19** - Latest React with modern hooks and patterns
- **TanStack Router** - File-based routing with type-safe navigation
- **Mantine UI** - Comprehensive component library
- **Role-Based Access Control** - Secure route protection by user roles
- **Modular Packages** - Independent, reusable role-specific modules
- **Turborepo** - Optimized build system with intelligent caching
- **pnpm Workspaces** - Efficient dependency management

## Getting Started

First, install the dependencies:

```bash
pnpm install
```

Then, run the development server:

```bash
pnpm dev
```

Open [http://localhost:3002](http://localhost:3002) in your browser to see the web application.

## 📁 Project Structure

```text
novaest/
├── apps/
│   └── web/                    # Main web application
│       ├── src/
│       │   ├── routes/         # TanStack Router routes
│       │   ├── contexts/       # Authentication & app contexts
│       │   ├── components/     # Shared UI components
│       │   └── utils/          # Utility functions
│       └── package.json
├── packages/
│   ├── shared/                 # Shared utilities & types
│   │   ├── src/
│   │   │   ├── types/          # TypeScript type definitions
│   │   │   ├── schemas/        # Zod validation schemas
│   │   │   └── utils/          # Common utility functions
│   │   └── package.json
│   ├── ui/                     # Shared UI component library
│   │   ├── src/
│   │   │   ├── components/     # Reusable UI components
│   │   │   └── styles/         # CSS and styling
│   │   └── package.json
│   ├── owner/                  # Owner role-specific package
│   │   ├── src/
│   │   │   ├── components/     # Owner dashboard components
│   │   │   ├── hooks/          # Owner-specific hooks
│   │   │   └── utils/          # Owner utility functions
│   │   └── package.json
│   ├── contractor/             # Contractor role-specific package
│   │   ├── src/
│   │   │   ├── components/     # Contractor dashboard components
│   │   │   ├── hooks/          # Contractor-specific hooks
│   │   │   └── utils/          # Contractor utility functions
│   │   └── package.json
│   └── admin/                  # Admin role-specific package
│       ├── src/
│       │   ├── components/     # Admin dashboard components
│       │   ├── hooks/          # Admin-specific hooks
│       │   └── utils/          # Admin utility functions
│       └── package.json
├── scripts/
│   └── dependency-check.js     # Dependency management utilities
├── turbo.json                  # Turborepo configuration
├── pnpm-workspace.yaml         # pnpm workspace configuration
└── package.json                # Root package configuration
```

## 🔧 Available Scripts

### Development
- `pnpm dev` - Start the web application in development mode
- `pnpm dev:ui` - Start UI package development with Storybook

### Building
- `pnpm build` - Build all packages and applications
- `pnpm build:packages` - Build only the packages
- `pnpm build:web` - Build only the web application
- `pnpm build:shared` - Build shared package
- `pnpm build:ui` - Build UI package
- `pnpm build:owner` - Build owner package
- `pnpm build:contractor` - Build contractor package
- `pnpm build:admin` - Build admin package

### Quality Assurance
- `pnpm check-types` - Check TypeScript types across all packages
- `pnpm lint` - Run Biome linting across all packages
- `pnpm format` - Format code with Biome

### Dependency Management
- `pnpm deps:check` - Run comprehensive dependency health check
- `pnpm deps:update` - Update all dependencies recursively
- `pnpm deps:audit` - Check for security vulnerabilities

### Maintenance
- `pnpm clean` - Clean build artifacts
- `pnpm clean:all` - Clean all node_modules and build artifacts

## 🏗️ Architecture Overview

### Role-Based Modular Design

This application implements a **role-based modular architecture** where each user role has its own dedicated package:

- **👑 Owner Package** (`@novaest/owner`) - Project management and oversight
- **🔨 Contractor Package** (`@novaest/contractor`) - Task execution and reporting
- **⚙️ Admin Package** (`@novaest/admin`) - User management and system administration

### Package Dependencies

```text
┌─────────────────┐
│   Web App       │
│  (apps/web)     │
└─────┬───────────┘
      │
      ├── @novaest/owner
      ├── @novaest/contractor
      ├── @novaest/admin
      ├── @novaest/ui
      └── @novaest/shared

┌─────────────────┐    ┌─────────────────┐
│ Role Packages   │    │ Foundation      │
│ (owner/         │    │ Packages        │
│  contractor/    │───▶│ (ui/shared)     │
│  admin)         │    │                 │
└─────────────────┘    └─────────────────┘
```

## 🔐 Authentication & Authorization

### Authentication Flow

1. **Login** - Users authenticate through `/login` route
2. **Role Detection** - System identifies user role (owner/contractor/admin)
3. **Route Protection** - TanStack Router guards protect role-specific routes
4. **Context Management** - Authentication state managed via React Context

### Route Protection

```typescript
// Example: Owner route protection
export const Route = createFileRoute('/owner')({
  beforeLoad: ({ context }) => {
    if (!context.auth.user || context.auth.user.role !== 'owner') {
      throw redirect({ to: '/login' })
    }
  }
})
```

### Role-Based Access Control

- **Owner Routes**: `/owner/*` - Project management dashboard
- **Contractor Routes**: `/contractor/*` - Task management interface
- **Admin Routes**: `/admin/*` - User and system administration
- **Public Routes**: `/`, `/login`, `/register` - Accessible to all

## 📚 Documentation

### 📖 **Comprehensive Guides**
- **[🏗️ Architecture Documentation](./docs/ARCHITECTURE.md)** - Detailed system architecture and design patterns
- **[🛠️ Development Guide](./docs/DEVELOPMENT.md)** - Complete development workflow and best practices
- **[📡 API Documentation](./docs/API.md)** - Package APIs and interfaces reference
- **[🚀 Deployment Guide](./docs/DEPLOYMENT.md)** - Production deployment and CI/CD setup

### 🎯 **Quick References**
- **Package Structure**: Each role has its own package with dedicated components and logic
- **Authentication**: Context-based auth with route-level protection
- **Styling**: Mantine UI components with custom theming
- **State Management**: TanStack Query for server state, React Context for app state
- **Build System**: Turborepo with intelligent caching and dependency management

## 🚀 Quick Start

### 1. **Installation**
```bash
# Clone repository
git clone <repository-url>
cd novaest

# Install dependencies
pnpm install
```

### 2. **Development**
```bash
# Start development server
pnpm dev

# Open http://localhost:3002
```

### 3. **Testing the Application**
```bash
# Login with test users:
# Owner: <EMAIL> / password
# Contractor: <EMAIL> / password
# Admin: <EMAIL> / password
```

### 4. **Building for Production**
```bash
# Build all packages
pnpm build

# Run dependency health check
pnpm deps:check
```

## 🧪 Testing

### Run Tests
```bash
# Run all tests
pnpm test

# Run tests for specific package
pnpm --filter @novaest/owner test

# Run integration tests
pnpm test:integration
```

### Quality Assurance
```bash
# Type checking
pnpm check-types

# Linting
pnpm lint

# Dependency audit
pnpm deps:audit
```

## 🔧 Development Tools

### Package Management
```bash
# Work on specific package
pnpm --filter @novaest/owner dev

# Build specific package
pnpm build:owner

# Add dependency to package
pnpm --filter @novaest/ui add @mantine/dates
```

### Maintenance
```bash
# Clean build artifacts
pnpm clean

# Update dependencies
pnpm deps:update

# Complete cleanup
pnpm clean:all
```

## 📊 Performance Metrics

- **Build Time**: ~9.5s (with Turborepo caching)
- **Bundle Size**: Optimized with code splitting
  - Main chunk: ~38kB
  - Vendor chunks: ~400kB total
  - Role chunks: Loaded on demand
- **Dependency Health**: 100/100 score
- **Type Safety**: 100% TypeScript coverage

## 🤝 Contributing

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/amazing-feature`
3. **Follow development guidelines**: See [Development Guide](./docs/DEVELOPMENT.md)
4. **Run quality checks**: `pnpm check-types && pnpm lint && pnpm test`
5. **Commit changes**: `git commit -m 'feat: add amazing feature'`
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open Pull Request**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **[TanStack Router](https://tanstack.com/router)** - Type-safe routing
- **[Mantine](https://mantine.dev/)** - React components library
- **[Turborepo](https://turbo.build/)** - Monorepo build system
- **[pnpm](https://pnpm.io/)** - Fast, disk space efficient package manager
- **[Biome](https://biomejs.dev/)** - Fast linter and formatter

---

**Built with ❤️ using modern TypeScript, React 19, and clean architecture principles.**
