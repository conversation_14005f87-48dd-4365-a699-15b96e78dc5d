{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"dev": {"cache": false, "persistent": true}, "build": {"dependsOn": [], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**"], "env": ["NODE_ENV"]}, "check-types": {"dependsOn": [], "inputs": ["$TURBO_DEFAULT$", "tsconfig*.json"], "outputs": ["*.tsbuildinfo"]}, "lint": {"dependsOn": [], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "biome.json*"], "outputs": []}, "clean": {"cache": false, "outputs": []}}, "globalDependencies": ["package.json", "pnpm-lock.yaml", "turbo.json", "tsconfig.json"]}