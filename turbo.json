{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": ["dist/**", ".turbo/**"], "env": ["NODE_ENV"]}, "dev": {"dependsOn": ["^build"], "cache": false, "persistent": true}, "dev:ui": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"], "inputs": ["$TURBO_DEFAULT$", ".eslintrc*", "biome.json*"], "outputs": []}, "check-types": {"dependsOn": ["^check-types"], "inputs": ["$TURBO_DEFAULT$", "tsconfig*.json"], "outputs": ["*.tsbuildinfo"]}, "clean": {"cache": false, "outputs": []}}, "globalDependencies": ["package.json", "pnpm-lock.yaml", "turbo.json", "tsconfig.json"]}