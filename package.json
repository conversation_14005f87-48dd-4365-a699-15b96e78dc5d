{"name": "novaest", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check apps packages --write", "lint": "biome check apps packages", "lint:fix": "biome check apps packages --write", "format": "biome format apps packages --write", "typecheck": "turbo check-types", "check:circular": "pnpm dlx dpdm -T --no-warning --no-tree **/*.ts", "dev": "pnpm --filter web dev", "build": "pnpm --filter web build", "build:analyze": "pnpm --filter web build --mode analyze", "clean": "turbo clean", "clean:all": "pnpm clean && pnpm -r exec rm -rf node_modules && rm -rf node_modules", "deps:check": "node scripts/dependency-check.js", "deps:update": "pnpm update --recursive", "deps:audit": "pnpm audit --audit-level moderate", "build:compare": "node scripts/build-comparison.js", "build:analysis": "node scripts/vite-only-analysis.js"}, "devDependencies": {"@biomejs/biome": "2.0.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "lefthook": "^1.11.14", "turbo": "^2.5.4"}, "packageManager": "pnpm@9.12.3"}