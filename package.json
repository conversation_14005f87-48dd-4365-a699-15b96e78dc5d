{"name": "novaest", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check apps packages --write", "lint": "biome check apps packages", "lint:fix": "biome check apps packages --write", "format": "biome format apps packages --write", "typecheck": "turbo check-types", "check:circular": "pnpm dlx dpdm -T --no-warning --no-tree **/*.ts", "dev": "turbo dev", "dev:web": "turbo -F web dev", "dev:ui": "turbo -F @novaest/ui dev:ui", "dev:shared": "turbo -F @novaest/shared dev", "dev:owner": "turbo -F @novaest/owner dev", "dev:contractor": "turbo -F @novaest/contractor dev", "dev:admin": "turbo -F @novaest/admin dev", "build": "turbo build", "build:ui": "turbo -F @novaest/ui build", "build:shared": "turbo -F @novaest/shared build", "build:owner": "turbo -F @novaest/owner build", "build:contractor": "turbo -F @novaest/contractor build", "build:admin": "turbo -F @novaest/admin build", "build:web": "turbo -F app build", "clean": "turbo clean", "clean:all": "pnpm clean && pnpm -r exec rm -rf node_modules && rm -rf node_modules", "deps:check": "node scripts/dependency-check.js", "deps:update": "pnpm update --recursive", "deps:audit": "pnpm audit --audit-level moderate"}, "devDependencies": {"@biomejs/biome": "2.0.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "lefthook": "^1.11.14", "turbo": "^2.5.4"}, "packageManager": "pnpm@9.12.3"}