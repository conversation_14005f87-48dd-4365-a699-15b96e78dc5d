{"name": "novaest", "private": true, "type": "module", "workspaces": ["apps/*", "packages/*"], "scripts": {"check": "biome check apps packages --write", "lint": "biome check apps packages", "lint:fix": "biome check apps packages --write", "format": "biome format apps packages --write", "typecheck": "turbo check-types", "check:circular": "pnpm dlx dpdm -T --no-warning --no-tree **/*.ts", "dev": "turbo dev", "dev:web": "turbo -F web dev", "dev:ui": "turbo -F @novaest/ui dev:ui", "build": "turbo build", "build:ui": "turbo -F @novaest/ui build", "build:web": "turbo -F web build"}, "devDependencies": {"@biomejs/biome": "2.0.5", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "lefthook": "^1.11.14", "turbo": "^2.5.4"}, "packageManager": "pnpm@9.12.3"}