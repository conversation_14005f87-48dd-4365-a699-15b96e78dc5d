{"editor.codeActionsOnSave": {"source.organizeImports.biome": "explicit", "quickfix.biome": "explicit", "source.fixAll.biome": "explicit"}, "editor.defaultFormatter": "biomejs.biome", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[svg]": {"editor.defaultFormatter": "jock.svg"}, "cSpell.words": ["<PERSON><PERSON>", "novaest"]}