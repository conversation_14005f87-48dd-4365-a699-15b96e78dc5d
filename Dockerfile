FROM node:20-buster AS base
RUN npm install -g pnpm@9

FROM base AS deps
WORKDIR /app

COPY package.json pnpm-lock.yaml* ./
COPY patches ./patches
RUN pnpm i --frozen-lockfile

FROM base AS builder
WORKDIR /app
ENV NODE_ENV production

COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN pnpm build

FROM base AS runner
WORKDIR /app

RUN npm install -g serve

ENV PORT 3000
ENV NODE_ENV production
EXPOSE 3000

COPY --from=builder /app/dist ./dist

CMD ["serve", "-s", "-l", "3000", "./dist"]
