#!/usr/bin/env node

/**
 * Convert packages to Vite-only build strategy
 * Removes tsdown dependencies and updates package.json files
 */

import fs from 'fs'
import path from 'path'

const packages = ['ui', 'owner', 'contractor', 'admin']

function updatePackageJson(packageName) {
  const packagePath = path.join('packages', packageName, 'package.json')

  if (!fs.existsSync(packagePath)) {
    console.log(`❌ Package not found: ${packagePath}`)
    return
  }

  const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'))

  // Update entry points to use source files
  packageJson.main = './src/index.ts'
  packageJson.module = './src/index.ts'
  packageJson.types = './src/index.ts'

  if (packageJson.exports) {
    packageJson.exports['.'] = {
      import: './src/index.ts',
      types: './src/index.ts'
    }
  }

  // Remove files field since we're not building
  delete packageJson.files

  // Update scripts
  packageJson.scripts = {
    'check-types': 'tsc --noEmit'
  }

  // Remove tsdown from devDependencies
  if (packageJson.devDependencies && packageJson.devDependencies.tsdown) {
    delete packageJson.devDependencies.tsdown
  }

  // Write back
  fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2) + '\n')
  console.log(`✅ Updated ${packageName} package.json`)
}

function removeTsdownConfigs() {
  packages.forEach((packageName) => {
    const configPath = path.join('packages', packageName, 'tsdown.config.ts')
    if (fs.existsSync(configPath)) {
      fs.unlinkSync(configPath)
      console.log(`🗑️  Removed ${packageName}/tsdown.config.ts`)
    }
  })
}

function updateTurboConfig() {
  const turboPath = 'turbo.json'
  if (!fs.existsSync(turboPath)) return

  const turboConfig = JSON.parse(fs.readFileSync(turboPath, 'utf8'))

  // Simplify turbo tasks since we're not building packages
  turboConfig.tasks = {
    dev: {
      cache: false,
      persistent: true
    },
    build: {
      dependsOn: [],
      inputs: ['$TURBO_DEFAULT$', '.env*'],
      outputs: ['dist/**'],
      env: ['NODE_ENV']
    },
    'check-types': {
      dependsOn: [],
      inputs: ['$TURBO_DEFAULT$', 'tsconfig*.json'],
      outputs: ['*.tsbuildinfo']
    },
    lint: {
      dependsOn: [],
      inputs: ['$TURBO_DEFAULT$', '.eslintrc*', 'biome.json*'],
      outputs: []
    },
    clean: {
      cache: false,
      outputs: []
    }
  }

  fs.writeFileSync(turboPath, JSON.stringify(turboConfig, null, 2) + '\n')
  console.log('✅ Updated turbo.json for Vite-only strategy')
}

function cleanDistFolders() {
  packages.forEach((packageName) => {
    const distPath = path.join('packages', packageName, 'dist')
    if (fs.existsSync(distPath)) {
      fs.rmSync(distPath, { recursive: true, force: true })
      console.log(`🧹 Cleaned ${packageName}/dist`)
    }
  })
}

function main() {
  console.log('🚀 Converting to Vite-only build strategy...\n')

  // Update all package.json files
  packages.forEach(updatePackageJson)

  // Remove tsdown configs
  removeTsdownConfigs()

  // Update turbo config
  updateTurboConfig()

  // Clean dist folders
  cleanDistFolders()

  console.log('\n✅ Conversion complete!')
  console.log('\n📋 Next steps:')
  console.log('1. Run `pnpm install` to update dependencies')
  console.log('2. Run `pnpm dev` to start development')
  console.log('3. Run `pnpm build` to test production build')
  console.log('4. Run `pnpm build:analyze` to analyze bundle')
}

// Run main function
main()
