#!/usr/bin/env node

/**
 * Build Comparison Script
 * Compares different build strategies and their performance
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function measureTime(fn) {
  const start = Date.now()
  fn()
  return Date.now() - start
}

function getBundleSize(distPath) {
  if (!fs.existsSync(distPath)) return 0
  
  let totalSize = 0
  const files = fs.readdirSync(distPath, { recursive: true })
  
  for (const file of files) {
    const filePath = path.join(distPath, file)
    if (fs.statSync(filePath).isFile()) {
      totalSize += fs.statSync(filePath).size
    }
  }
  
  return totalSize
}

function formatSize(bytes) {
  const sizes = ['B', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 B'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`
}

function cleanDist() {
  log('🧹 Cleaning previous builds...', 'yellow')
  try {
    execSync('pnpm clean', { stdio: 'pipe' })
  } catch (error) {
    // Ignore errors
  }
}

function testTraditionalBuild() {
  log('\n📦 Testing Traditional Build (packages + web)...', 'blue')
  
  cleanDist()
  
  const time = measureTime(() => {
    try {
      execSync('pnpm build:packages', { stdio: 'pipe' })
      execSync('pnpm build:web', { stdio: 'pipe' })
    } catch (error) {
      log(`❌ Traditional build failed: ${error.message}`, 'red')
      return null
    }
  })
  
  const bundleSize = getBundleSize('apps/web/dist')
  
  return {
    name: 'Traditional Build',
    time,
    bundleSize,
    description: 'Build packages first, then web app'
  }
}

function testViteOnlyBuild() {
  log('\n⚡ Testing Vite-Only Build (direct from source)...', 'blue')
  
  cleanDist()
  
  const time = measureTime(() => {
    try {
      execSync('pnpm build:web-only', { stdio: 'pipe' })
    } catch (error) {
      log(`❌ Vite-only build failed: ${error.message}`, 'red')
      return null
    }
  })
  
  const bundleSize = getBundleSize('apps/web/dist')
  
  return {
    name: 'Vite-Only Build',
    time,
    bundleSize,
    description: 'Build web app directly from package sources'
  }
}

function testTurboBuild() {
  log('\n🚀 Testing Turbo Build (optimized)...', 'blue')
  
  cleanDist()
  
  const time = measureTime(() => {
    try {
      execSync('pnpm build', { stdio: 'pipe' })
    } catch (error) {
      log(`❌ Turbo build failed: ${error.message}`, 'red')
      return null
    }
  })
  
  const bundleSize = getBundleSize('apps/web/dist')
  
  return {
    name: 'Turbo Build',
    time,
    bundleSize,
    description: 'Full Turbo build with caching'
  }
}

function displayResults(results) {
  log('\n📊 Build Comparison Results', 'bright')
  log('=' * 60, 'cyan')
  
  const validResults = results.filter(r => r !== null)
  
  if (validResults.length === 0) {
    log('❌ No successful builds to compare', 'red')
    return
  }
  
  // Sort by build time
  validResults.sort((a, b) => a.time - b.time)
  
  validResults.forEach((result, index) => {
    const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'
    const timeColor = index === 0 ? 'green' : index === 1 ? 'yellow' : 'red'
    
    log(`\n${medal} ${result.name}`, 'bright')
    log(`   Description: ${result.description}`, 'cyan')
    log(`   Build Time: ${result.time}ms`, timeColor)
    log(`   Bundle Size: ${formatSize(result.bundleSize)}`, 'magenta')
  })
  
  // Performance comparison
  if (validResults.length > 1) {
    const fastest = validResults[0]
    const slowest = validResults[validResults.length - 1]
    const improvement = ((slowest.time - fastest.time) / slowest.time * 100).toFixed(1)
    
    log(`\n⚡ Performance Improvement: ${improvement}% faster`, 'green')
  }
}

function analyzeTradeoffs() {
  log('\n🤔 Build Strategy Analysis', 'bright')
  log('=' * 60, 'cyan')
  
  log('\n📦 Traditional Build (tsdown + Vite)', 'yellow')
  log('   ✅ Package independence and reusability')
  log('   ✅ Better type checking and validation')
  log('   ✅ Incremental builds with Turborepo')
  log('   ❌ Slower initial builds')
  log('   ❌ More complex build pipeline')
  
  log('\n⚡ Vite-Only Build', 'yellow')
  log('   ✅ Faster development and builds')
  log('   ✅ Simpler configuration')
  log('   ✅ Better HMR performance')
  log('   ❌ Packages not independently buildable')
  log('   ❌ Cannot publish packages separately')
  
  log('\n🚀 Turbo Build (Optimized)', 'yellow')
  log('   ✅ Best of both worlds with caching')
  log('   ✅ Intelligent dependency management')
  log('   ✅ Parallel builds when possible')
  log('   ❌ Initial setup complexity')
}

function main() {
  log('🔍 Build Strategy Comparison Tool', 'bright')
  log('Testing different build approaches for the modular architecture\n', 'cyan')
  
  const results = [
    testTraditionalBuild(),
    testViteOnlyBuild(),
    testTurboBuild()
  ]
  
  displayResults(results)
  analyzeTradeoffs()
  
  log('\n💡 Recommendation', 'bright')
  log('=' * 60, 'cyan')
  log('For this modular architecture, the HYBRID approach is recommended:')
  log('• Use `pnpm dev:fast` for rapid development (Vite-only)')
  log('• Use `pnpm dev` for full development with package builds')
  log('• Use `pnpm build` for production (full Turbo build)')
  log('• This gives you flexibility based on your current needs', 'green')
}

if (require.main === module) {
  main()
}

module.exports = {
  testTraditionalBuild,
  testViteOnlyBuild,
  testTurboBuild,
  displayResults,
  analyzeTradeoffs
}
