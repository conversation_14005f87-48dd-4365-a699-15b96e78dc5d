#!/usr/bin/env node

/**
 * Dependency Management Checker
 * 
 * This script checks for:
 * - Version consistency across packages
 * - Missing peer dependencies
 * - Duplicate dependencies
 * - Unused dependencies
 * - Security vulnerabilities
 */

import { execSync } from 'child_process'
import { readFileSync, existsSync } from 'fs'
import { join } from 'path'

const WORKSPACE_ROOT = process.cwd()
const PACKAGES = [
  'apps/web',
  'packages/shared',
  'packages/ui',
  'packages/owner',
  'packages/contractor',
  'packages/admin'
]

class DependencyChecker {
  constructor() {
    this.issues = []
    this.warnings = []
    this.info = []
  }

  log(type, message) {
    const timestamp = new Date().toISOString()
    const prefix = {
      error: '❌',
      warning: '⚠️',
      info: 'ℹ️',
      success: '✅'
    }[type] || 'ℹ️'
    
    console.log(`${prefix} ${message}`)
    
    if (type === 'error') this.issues.push(message)
    if (type === 'warning') this.warnings.push(message)
    if (type === 'info') this.info.push(message)
  }

  readPackageJson(packagePath) {
    const fullPath = join(WORKSPACE_ROOT, packagePath, 'package.json')
    if (!existsSync(fullPath)) {
      this.log('error', `Package.json not found: ${fullPath}`)
      return null
    }
    
    try {
      return JSON.parse(readFileSync(fullPath, 'utf8'))
    } catch (error) {
      this.log('error', `Failed to parse package.json: ${fullPath} - ${error.message}`)
      return null
    }
  }

  checkVersionConsistency() {
    this.log('info', '🔍 Checking version consistency across packages...')
    
    const allDependencies = new Map()
    
    // Collect all dependencies from all packages
    for (const packagePath of PACKAGES) {
      const pkg = this.readPackageJson(packagePath)
      if (!pkg) continue
      
      const deps = {
        ...pkg.dependencies,
        ...pkg.devDependencies,
        ...pkg.peerDependencies
      }
      
      for (const [name, version] of Object.entries(deps)) {
        if (name.startsWith('@novaest/')) continue // Skip workspace packages
        
        if (!allDependencies.has(name)) {
          allDependencies.set(name, new Map())
        }
        
        allDependencies.get(name).set(packagePath, version)
      }
    }
    
    // Check for version mismatches
    let inconsistencies = 0
    for (const [depName, packages] of allDependencies) {
      const versions = new Set(packages.values())
      if (versions.size > 1) {
        inconsistencies++
        this.log('warning', `Version mismatch for ${depName}:`)
        for (const [pkg, version] of packages) {
          console.log(`  ${pkg}: ${version}`)
        }
      }
    }
    
    if (inconsistencies === 0) {
      this.log('success', 'All dependency versions are consistent')
    } else {
      this.log('warning', `Found ${inconsistencies} version inconsistencies`)
    }
  }

  checkPeerDependencies() {
    this.log('info', '🔍 Checking peer dependencies...')
    
    for (const packagePath of PACKAGES) {
      const pkg = this.readPackageJson(packagePath)
      if (!pkg) continue
      
      const peerDeps = pkg.peerDependencies || {}
      const devDeps = pkg.devDependencies || {}
      
      for (const peerDep of Object.keys(peerDeps)) {
        if (!devDeps[peerDep]) {
          this.log('warning', `${packagePath}: Peer dependency ${peerDep} not in devDependencies`)
        }
      }
    }
  }

  checkWorkspaceDependencies() {
    this.log('info', '🔍 Checking workspace dependencies...')
    
    for (const packagePath of PACKAGES) {
      const pkg = this.readPackageJson(packagePath)
      if (!pkg) continue
      
      const deps = pkg.dependencies || {}
      
      for (const [name, version] of Object.entries(deps)) {
        if (name.startsWith('@novaest/') && version !== 'workspace:*') {
          this.log('warning', `${packagePath}: Workspace dependency ${name} should use 'workspace:*', found: ${version}`)
        }
      }
    }
  }

  checkBuildDependencies() {
    this.log('info', '🔍 Checking build dependencies...')
    
    const requiredBuildDeps = ['typescript', 'tsdown']
    
    for (const packagePath of PACKAGES) {
      if (packagePath === 'apps/web') continue // Web app has different build setup
      
      const pkg = this.readPackageJson(packagePath)
      if (!pkg) continue
      
      const devDeps = pkg.devDependencies || {}
      
      for (const dep of requiredBuildDeps) {
        if (!devDeps[dep]) {
          this.log('warning', `${packagePath}: Missing build dependency: ${dep}`)
        }
      }
    }
  }

  checkSecurityVulnerabilities() {
    this.log('info', '🔍 Checking for security vulnerabilities...')
    
    try {
      const result = execSync('pnpm audit --audit-level moderate', { 
        encoding: 'utf8',
        cwd: WORKSPACE_ROOT 
      })
      this.log('success', 'No security vulnerabilities found')
    } catch (error) {
      if (error.status === 1) {
        this.log('warning', 'Security vulnerabilities found. Run `pnpm audit` for details')
      } else {
        this.log('error', `Security audit failed: ${error.message}`)
      }
    }
  }

  checkDuplicates() {
    this.log('info', '🔍 Checking for duplicate dependencies...')
    
    try {
      const result = execSync('pnpm list --depth=0 --long', { 
        encoding: 'utf8',
        cwd: WORKSPACE_ROOT 
      })
      
      // pnpm automatically deduplicates, so we just verify it's working
      this.log('success', 'pnpm is handling dependency deduplication')
    } catch (error) {
      this.log('error', `Failed to check duplicates: ${error.message}`)
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60))
    console.log('📊 DEPENDENCY MANAGEMENT REPORT')
    console.log('='.repeat(60))
    
    console.log(`\n✅ Successful checks: ${this.info.length}`)
    console.log(`⚠️  Warnings: ${this.warnings.length}`)
    console.log(`❌ Errors: ${this.issues.length}`)
    
    if (this.issues.length > 0) {
      console.log('\n🚨 CRITICAL ISSUES:')
      this.issues.forEach(issue => console.log(`  • ${issue}`))
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:')
      this.warnings.forEach(warning => console.log(`  • ${warning}`))
    }
    
    console.log('\n💡 RECOMMENDATIONS:')
    console.log('  • Run `pnpm update` to update dependencies')
    console.log('  • Run `pnpm audit --fix` to fix security issues')
    console.log('  • Use `pnpm dedupe` to remove duplicate dependencies')
    console.log('  • Consider using `pnpm-lock.yaml` in version control')
    
    const score = Math.max(0, 100 - (this.issues.length * 20) - (this.warnings.length * 5))
    console.log(`\n🎯 Dependency Health Score: ${score}/100`)
    
    return this.issues.length === 0
  }

  async run() {
    console.log('🚀 Starting dependency management check...\n')
    
    this.checkVersionConsistency()
    this.checkPeerDependencies()
    this.checkWorkspaceDependencies()
    this.checkBuildDependencies()
    this.checkSecurityVulnerabilities()
    this.checkDuplicates()
    
    return this.generateReport()
  }
}

// Run the checker
const checker = new DependencyChecker()
checker.run().then(success => {
  process.exit(success ? 0 : 1)
}).catch(error => {
  console.error('❌ Dependency check failed:', error)
  process.exit(1)
})
