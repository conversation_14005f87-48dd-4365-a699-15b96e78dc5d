{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto"}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off", "useLiteralEnumMembers": "error", "useNodejsImportProtocol": "error", "useAsConstAssertion": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useConst": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "useExponentiationOperator": "error", "useTemplate": "error", "noParameterAssign": "error", "useDefaultParameterLast": "error", "useImportType": "error", "useExportType": "error", "noUselessElse": "error", "useShorthandFunctionType": "error"}, "correctness": {"noUnusedImports": "error", "useHookAtTopLevel": "off"}, "suspicious": {"noArrayIndexKey": "off"}, "nursery": {"noImportCycles": "error"}}, "domains": {"react": "recommended"}}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "none", "semicolons": "asNeeded", "arrowParentheses": "always", "bracketSpacing": true, "bracketSameLine": false, "quoteStyle": "single", "attributePosition": "auto"}}, "files": {"includes": ["**"], "ignoreUnknown": true}}