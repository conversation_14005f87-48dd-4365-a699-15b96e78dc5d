# 🏗️ Architecture Documentation

## Overview

Novaest implements a **role-based modular architecture** designed for scalability, maintainability, and clear separation of concerns. The system is built around three core user roles, each with dedicated packages and functionality.

## 🎯 Design Principles

### 1. **Clean Architecture**
- **Separation of Concerns**: Each package has a single responsibility
- **Dependency Inversion**: High-level modules don't depend on low-level modules
- **Interface Segregation**: Packages expose only necessary interfaces

### 2. **Role-Based Design**
- **Domain-Driven**: Packages organized by business domains (roles)
- **Encapsulation**: Role-specific logic contained within respective packages
- **Scalability**: Easy to add new roles or modify existing ones

### 3. **Modular Structure**
- **Independent Packages**: Each package can be developed and tested independently
- **Shared Dependencies**: Common functionality centralized in shared packages
- **Type Safety**: Full TypeScript coverage across all packages

## 📦 Package Architecture

### Core Packages

#### 1. **@novaest/shared** - Foundation Layer
```typescript
// Types and interfaces
export interface User {
  id: string
  email: string
  role: 'owner' | 'contractor' | 'admin'
  name: string
}

// Validation schemas
export const userSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  role: z.enum(['owner', 'contractor', 'admin']),
  name: z.string().min(1)
})

// Utility functions
export const formatDate = (date: Date): string => {
  return date.toLocaleDateString()
}
```

**Responsibilities:**
- Type definitions and interfaces
- Validation schemas (Zod)
- Common utility functions
- Constants and enums

#### 2. **@novaest/ui** - Presentation Layer
```typescript
// Component exports
export { Button } from './components/Button'
export { Card } from './components/Card'
export { DataTable } from './components/DataTable'

// Mantine re-exports with customization
export { 
  MantineProvider,
  createTheme,
  Button as MantineButton 
} from '@mantine/core'
```

**Responsibilities:**
- Reusable UI components
- Design system implementation
- Mantine UI customizations
- Styling and theming

### Role-Specific Packages

#### 3. **@novaest/owner** - Owner Domain
```typescript
// Owner-specific components
export const ProjectDashboard: React.FC = () => {
  return (
    <div>
      <ProjectList />
      <ProjectMetrics />
      <ContractorManagement />
    </div>
  )
}

// Owner-specific hooks
export const useProjectManagement = () => {
  // Project management logic
}
```

**Responsibilities:**
- Project creation and management
- Contractor oversight
- Financial tracking
- Progress monitoring

#### 4. **@novaest/contractor** - Contractor Domain
```typescript
// Contractor-specific components
export const TaskDashboard: React.FC = () => {
  return (
    <div>
      <TaskList />
      <TimeTracking />
      <ProgressReporting />
    </div>
  )
}

// Contractor-specific hooks
export const useTaskManagement = () => {
  // Task management logic
}
```

**Responsibilities:**
- Task execution and tracking
- Time management
- Progress reporting
- Communication with owners

#### 5. **@novaest/admin** - Admin Domain
```typescript
// Admin-specific components
export const AdminDashboard: React.FC = () => {
  return (
    <div>
      <UserManagement />
      <SystemMetrics />
      <SecuritySettings />
    </div>
  )
}

// Admin-specific hooks
export const useUserManagement = () => {
  // User management logic
}
```

**Responsibilities:**
- User management
- System administration
- Security and permissions
- Analytics and reporting

## 🔄 Data Flow Architecture

### 1. **Authentication Flow**
```mermaid
graph TD
    A[User Login] --> B[Authentication Context]
    B --> C{Role Check}
    C -->|Owner| D[Owner Routes]
    C -->|Contractor| E[Contractor Routes]
    C -->|Admin| F[Admin Routes]
    C -->|Unauthorized| G[Redirect to Login]
```

### 2. **Component Hierarchy**
```mermaid
graph TD
    A[Web App] --> B[Authentication Provider]
    B --> C[Router Provider]
    C --> D[Role-Specific Routes]
    D --> E[Package Components]
    E --> F[Shared UI Components]
    F --> G[Mantine Components]
```

### 3. **Package Dependencies**
```mermaid
graph TD
    A[Web App] --> B[@novaest/owner]
    A --> C[@novaest/contractor]
    A --> D[@novaest/admin]
    B --> E[@novaest/shared]
    C --> E
    D --> E
    B --> F[@novaest/ui]
    C --> F
    D --> F
    F --> E
```

## 🛡️ Security Architecture

### Authentication & Authorization

#### 1. **Route Guards**
```typescript
// TanStack Router beforeLoad hook
export const Route = createFileRoute('/owner')({
  beforeLoad: ({ context }) => {
    const { user } = context.auth
    
    if (!user) {
      throw redirect({ to: '/login' })
    }
    
    if (user.role !== 'owner') {
      throw redirect({ to: '/unauthorized' })
    }
  }
})
```

#### 2. **Context-Based Security**
```typescript
// Authentication context
interface AuthContext {
  user: User | null
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  hasRole: (role: UserRole) => boolean
  hasPermission: (permission: string) => boolean
}
```

#### 3. **Package-Level Security**
- Each package validates user permissions
- Components check authorization before rendering
- API calls include authentication headers

## 🚀 Performance Architecture

### 1. **Code Splitting**
```typescript
// Vite configuration for optimal chunking
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          if (id.includes('@novaest/owner')) return 'role-owner'
          if (id.includes('@novaest/contractor')) return 'role-contractor'
          if (id.includes('@novaest/admin')) return 'role-admin'
          if (id.includes('@mantine')) return 'vendor-mantine'
          if (id.includes('react')) return 'vendor-react'
        }
      }
    }
  }
})
```

### 2. **Build Optimization**
- **Turborepo Caching**: Intelligent build caching across packages
- **Dependency Optimization**: Shared dependencies to reduce bundle size
- **Tree Shaking**: Unused code elimination
- **Lazy Loading**: Role-specific packages loaded on demand

### 3. **Development Performance**
- **Hot Module Replacement**: Fast development feedback
- **Incremental Builds**: Only rebuild changed packages
- **Type Checking**: Parallel type checking across packages

## 🧪 Testing Architecture

### 1. **Package-Level Testing**
```typescript
// Example: Owner package test
describe('@novaest/owner', () => {
  it('should render project dashboard for owner role', () => {
    render(<ProjectDashboard />, {
      wrapper: ({ children }) => (
        <AuthProvider user={{ role: 'owner' }}>
          {children}
        </AuthProvider>
      )
    })
    
    expect(screen.getByText('Project Dashboard')).toBeInTheDocument()
  })
})
```

### 2. **Integration Testing**
```typescript
// Example: Authentication integration test
describe('Authentication Integration', () => {
  it('should redirect to correct dashboard based on role', async () => {
    const { user } = renderWithAuth('/login')
    
    await user.type(screen.getByLabelText('Email'), '<EMAIL>')
    await user.type(screen.getByLabelText('Password'), 'password')
    await user.click(screen.getByRole('button', { name: 'Login' }))
    
    expect(window.location.pathname).toBe('/owner')
  })
})
```

### 3. **Testing Strategy**
- **Unit Tests**: Individual package components and utilities
- **Integration Tests**: Cross-package interactions
- **E2E Tests**: Complete user workflows
- **Type Tests**: TypeScript type safety validation

## 📈 Scalability Considerations

### 1. **Adding New Roles**
```bash
# Create new role package
mkdir packages/manager
cd packages/manager

# Initialize package
pnpm init
# Add dependencies and configuration
# Implement role-specific components
```

### 2. **Package Extension**
- Each package can be extended independently
- New features added without affecting other packages
- Shared functionality centralized in foundation packages

### 3. **Performance Scaling**
- Lazy loading prevents initial bundle bloat
- Caching strategies reduce build times
- Modular architecture supports team scaling

## 🔧 Development Workflow

### 1. **Package Development**
```bash
# Develop specific package
pnpm --filter @novaest/owner dev

# Build specific package
pnpm --filter @novaest/owner build

# Test specific package
pnpm --filter @novaest/owner test
```

### 2. **Cross-Package Development**
```bash
# Build all dependencies
pnpm build:packages

# Start development with all packages
pnpm dev
```

### 3. **Quality Assurance**
```bash
# Check all packages
pnpm check-types
pnpm lint
pnpm test

# Dependency health check
pnpm deps:check
```

This architecture provides a solid foundation for building scalable, maintainable applications with clear separation of concerns and excellent developer experience.
