# 📡 API Documentation

## Package APIs

This document outlines the public APIs exposed by each package in the Novaest modular architecture.

## 🏗️ @novaest/shared

### Types and Interfaces

```typescript
// User Management
export interface User {
  id: string
  email: string
  name: string
  role: 'owner' | 'contractor' | 'admin'
  createdAt: Date
  updatedAt: Date
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface AuthResponse {
  user: User
  token: string
  expiresAt: Date
}

// Project Management
export interface Project {
  id: string
  name: string
  description: string
  status: 'planning' | 'active' | 'completed' | 'cancelled'
  ownerId: string
  contractorIds: string[]
  startDate: Date
  endDate?: Date
  budget: number
  progress: number
}

// Task Management
export interface Task {
  id: string
  projectId: string
  title: string
  description: string
  status: 'todo' | 'in-progress' | 'review' | 'completed'
  assignedTo: string
  priority: 'low' | 'medium' | 'high' | 'urgent'
  dueDate?: Date
  estimatedHours: number
  actualHours: number
}
```

### Validation Schemas

```typescript
// Zod schemas for runtime validation
export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().min(1).max(100),
  role: z.enum(['owner', 'contractor', 'admin']),
  createdAt: z.date(),
  updatedAt: z.date()
})

export const projectSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(200),
  description: z.string().max(1000),
  status: z.enum(['planning', 'active', 'completed', 'cancelled']),
  ownerId: z.string().uuid(),
  contractorIds: z.array(z.string().uuid()),
  startDate: z.date(),
  endDate: z.date().optional(),
  budget: z.number().positive(),
  progress: z.number().min(0).max(100)
})

export const taskSchema = z.object({
  id: z.string().uuid(),
  projectId: z.string().uuid(),
  title: z.string().min(1).max(200),
  description: z.string().max(1000),
  status: z.enum(['todo', 'in-progress', 'review', 'completed']),
  assignedTo: z.string().uuid(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  dueDate: z.date().optional(),
  estimatedHours: z.number().positive(),
  actualHours: z.number().min(0)
})
```

### Utility Functions

```typescript
// Date utilities
export const formatDate = (date: Date, format?: string): string
export const formatRelativeTime = (date: Date): string
export const isDateInRange = (date: Date, start: Date, end: Date): boolean

// String utilities
export const slugify = (text: string): string
export const truncate = (text: string, length: number): string
export const capitalize = (text: string): string

// Number utilities
export const formatCurrency = (amount: number, currency?: string): string
export const formatPercentage = (value: number, decimals?: number): string
export const clamp = (value: number, min: number, max: number): number

// Validation utilities
export const validateEmail = (email: string): boolean
export const validatePassword = (password: string): boolean
export const validateUUID = (id: string): boolean
```

## 🎨 @novaest/ui

### Components

```typescript
// Basic Components
export interface ButtonProps {
  variant?: 'filled' | 'outline' | 'subtle'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  color?: string
  loading?: boolean
  disabled?: boolean
  onClick?: () => void
  children: React.ReactNode
}
export const Button: React.FC<ButtonProps>

export interface CardProps {
  shadow?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  padding?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  radius?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  withBorder?: boolean
  children: React.ReactNode
}
export const Card: React.FC<CardProps>

// Data Display Components
export interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  loading?: boolean
  pagination?: boolean
  sorting?: boolean
  filtering?: boolean
  onRowClick?: (row: T) => void
}
export const DataTable: <T>(props: DataTableProps<T>) => JSX.Element

export interface StatsCardProps {
  title: string
  value: string | number
  description?: string
  icon?: React.ReactNode
  trend?: {
    value: number
    label: string
    direction: 'up' | 'down'
  }
}
export const StatsCard: React.FC<StatsCardProps>

// Form Components
export interface FormFieldProps {
  label: string
  description?: string
  error?: string
  required?: boolean
  children: React.ReactNode
}
export const FormField: React.FC<FormFieldProps>

export interface SearchInputProps {
  placeholder?: string
  value: string
  onChange: (value: string) => void
  onSearch?: (value: string) => void
  debounceMs?: number
}
export const SearchInput: React.FC<SearchInputProps>
```

### Hooks

```typescript
// UI State Hooks
export const useDisclosure = (initial?: boolean): [
  boolean,
  { open: () => void; close: () => void; toggle: () => void }
]

export const useLocalStorage = <T>(
  key: string,
  defaultValue: T
): [T, (value: T) => void]

export const useDebounce = <T>(value: T, delay: number): T

// Form Hooks
export const useForm = <T>(config: {
  initialValues: T
  validate?: (values: T) => Record<string, string>
  onSubmit: (values: T) => void
}): {
  values: T
  errors: Record<string, string>
  setFieldValue: (field: keyof T, value: any) => void
  handleSubmit: () => void
  reset: () => void
}
```

## 👑 @novaest/owner

### Components

```typescript
// Dashboard Components
export const OwnerDashboard: React.FC = () => JSX.Element
export const ProjectOverview: React.FC = () => JSX.Element
export const ContractorManagement: React.FC = () => JSX.Element

// Project Management
export interface ProjectListProps {
  projects?: Project[]
  loading?: boolean
  onProjectClick?: (project: Project) => void
  onCreateProject?: () => void
}
export const ProjectList: React.FC<ProjectListProps>

export interface ProjectFormProps {
  project?: Partial<Project>
  onSubmit: (project: Project) => void
  onCancel: () => void
}
export const ProjectForm: React.FC<ProjectFormProps>

export interface ProjectMetricsProps {
  projectId: string
}
export const ProjectMetrics: React.FC<ProjectMetricsProps>

// Contractor Management
export interface ContractorListProps {
  contractors: User[]
  onInvite?: () => void
  onRemove?: (contractorId: string) => void
}
export const ContractorList: React.FC<ContractorListProps>
```

### Hooks

```typescript
// Project Management Hooks
export const useProjects = (): {
  projects: Project[]
  loading: boolean
  error: string | null
  createProject: (project: Omit<Project, 'id'>) => Promise<void>
  updateProject: (id: string, updates: Partial<Project>) => Promise<void>
  deleteProject: (id: string) => Promise<void>
  refetch: () => void
}

export const useProjectMetrics = (projectId: string): {
  metrics: {
    totalTasks: number
    completedTasks: number
    totalHours: number
    budget: number
    spent: number
    progress: number
  }
  loading: boolean
  error: string | null
}

// Contractor Management Hooks
export const useContractors = (): {
  contractors: User[]
  loading: boolean
  inviteContractor: (email: string) => Promise<void>
  removeContractor: (id: string) => Promise<void>
}
```

## 🔨 @novaest/contractor

### Components

```typescript
// Dashboard Components
export const ContractorDashboard: React.FC = () => JSX.Element
export const TaskOverview: React.FC = () => JSX.Element
export const TimeTracking: React.FC = () => JSX.Element

// Task Management
export interface TaskListProps {
  tasks?: Task[]
  loading?: boolean
  onTaskClick?: (task: Task) => void
  filter?: {
    status?: Task['status']
    priority?: Task['priority']
    projectId?: string
  }
}
export const TaskList: React.FC<TaskListProps>

export interface TaskCardProps {
  task: Task
  onClick?: () => void
  onStatusChange?: (status: Task['status']) => void
}
export const TaskCard: React.FC<TaskCardProps>

export interface TimeEntryFormProps {
  taskId: string
  onSubmit: (entry: { hours: number; description: string }) => void
}
export const TimeEntryForm: React.FC<TimeEntryFormProps>
```

### Hooks

```typescript
// Task Management Hooks
export const useTasks = (filter?: {
  projectId?: string
  status?: Task['status']
}): {
  tasks: Task[]
  loading: boolean
  error: string | null
  updateTaskStatus: (id: string, status: Task['status']) => Promise<void>
  logTime: (taskId: string, hours: number, description: string) => Promise<void>
}

export const useTimeTracking = (): {
  activeTask: Task | null
  startTimer: (taskId: string) => void
  stopTimer: () => void
  elapsedTime: number
  timeEntries: TimeEntry[]
}
```

## ⚙️ @novaest/admin

### Components

```typescript
// Dashboard Components
export const AdminDashboard: React.FC = () => JSX.Element
export const SystemMetrics: React.FC = () => JSX.Element
export const SecuritySettings: React.FC = () => JSX.Element

// User Management
export interface UserManagementProps {
  users?: User[]
  loading?: boolean
  onUserClick?: (user: User) => void
  onCreateUser?: () => void
}
export const UserManagement: React.FC<UserManagementProps>

export interface UserFormProps {
  user?: Partial<User>
  onSubmit: (user: User) => void
  onCancel: () => void
}
export const UserForm: React.FC<UserFormProps>

export interface RoleManagementProps {
  userId: string
  currentRole: User['role']
  onRoleChange: (role: User['role']) => void
}
export const RoleManagement: React.FC<RoleManagementProps>
```

### Hooks

```typescript
// User Management Hooks
export const useUsers = (): {
  users: User[]
  loading: boolean
  error: string | null
  createUser: (user: Omit<User, 'id'>) => Promise<void>
  updateUser: (id: string, updates: Partial<User>) => Promise<void>
  deleteUser: (id: string) => Promise<void>
  changeUserRole: (id: string, role: User['role']) => Promise<void>
}

// System Management Hooks
export const useSystemMetrics = (): {
  metrics: {
    totalUsers: number
    activeProjects: number
    completedTasks: number
    systemHealth: 'good' | 'warning' | 'critical'
  }
  loading: boolean
  error: string | null
}
```

## 🔧 Usage Examples

### Authentication Flow

```typescript
import { useAuth } from '@/contexts/AuthContext'
import { LoginCredentials, userSchema } from '@novaest/shared'

const LoginForm = () => {
  const { login } = useAuth()
  
  const handleSubmit = async (credentials: LoginCredentials) => {
    try {
      await login(credentials)
      // User will be redirected based on role
    } catch (error) {
      console.error('Login failed:', error)
    }
  }
  
  return (
    <form onSubmit={handleSubmit}>
      {/* Form implementation */}
    </form>
  )
}
```

### Role-Specific Component Usage

```typescript
// Owner Dashboard
import { ProjectList, ProjectMetrics } from '@novaest/owner'
import { Card } from '@novaest/ui'

const OwnerPage = () => (
  <div>
    <Card>
      <ProjectList onProjectClick={(project) => navigate(`/projects/${project.id}`)} />
    </Card>
    <Card>
      <ProjectMetrics projectId="current-project-id" />
    </Card>
  </div>
)

// Contractor Dashboard
import { TaskList, TimeTracking } from '@novaest/contractor'

const ContractorPage = () => (
  <div>
    <TaskList filter={{ status: 'in-progress' }} />
    <TimeTracking />
  </div>
)
```

### Data Validation

```typescript
import { projectSchema, taskSchema } from '@novaest/shared'

const validateProjectData = (data: unknown) => {
  try {
    const project = projectSchema.parse(data)
    return { success: true, data: project }
  } catch (error) {
    return { success: false, error: error.message }
  }
}
```

This API documentation provides a comprehensive reference for all public interfaces exposed by the modular packages. Use these APIs to build consistent, type-safe applications within the Novaest architecture.
