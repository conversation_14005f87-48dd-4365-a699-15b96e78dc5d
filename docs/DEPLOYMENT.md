# 🚀 Deployment Guide

## Overview

This guide covers deployment strategies for the Novaest modular architecture, including build optimization, environment configuration, and deployment to various platforms.

## 📋 Pre-Deployment Checklist

### 1. **Code Quality Verification**
```bash
# Run complete quality check
pnpm deps:check          # Dependency health check
pnpm check-types         # TypeScript validation
pnpm lint               # Code linting
pnpm test               # Run all tests
pnpm build              # Production build
```

### 2. **Security Audit**
```bash
# Check for vulnerabilities
pnpm deps:audit

# Update dependencies if needed
pnpm deps:update

# Verify no high-severity issues
pnpm audit --audit-level high
```

### 3. **Performance Validation**
```bash
# Analyze bundle sizes
pnpm build:web --analyze

# Check build performance
time pnpm build

# Verify code splitting
ls -la apps/web/dist/assets/
```

## 🏗️ Build Configuration

### 1. **Production Build Setup**

**Environment Variables:**
```bash
# .env.production
NODE_ENV=production
VITE_API_URL=https://api.yourdomain.com
VITE_APP_VERSION=1.0.0
VITE_SENTRY_DSN=your-sentry-dsn
```

**Build Command:**
```bash
# Production build with optimizations
NODE_ENV=production pnpm build

# Verify build output
ls -la apps/web/dist/
```

### 2. **Build Optimization**

**Vite Configuration (apps/web/vite.config.ts):**
```typescript
export default defineConfig({
  build: {
    // Optimize for production
    minify: 'terser',
    sourcemap: false,
    
    // Code splitting configuration
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunks
          if (id.includes('node_modules')) {
            if (id.includes('react')) return 'vendor-react'
            if (id.includes('@tanstack')) return 'vendor-tanstack'
            if (id.includes('@mantine')) return 'vendor-mantine'
            return 'vendor'
          }
          
          // Role-based chunks
          if (id.includes('@novaest/owner')) return 'role-owner'
          if (id.includes('@novaest/contractor')) return 'role-contractor'
          if (id.includes('@novaest/admin')) return 'role-admin'
          if (id.includes('@novaest/shared')) return 'shared'
        }
      }
    },
    
    // Performance settings
    chunkSizeWarningLimit: 600,
    assetsInlineLimit: 4096
  }
})
```

## 🌐 Platform Deployment

### 1. **Vercel Deployment**

**vercel.json:**
```json
{
  "version": 2,
  "builds": [
    {
      "src": "apps/web/package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "buildCommand": "cd ../.. && pnpm build:web",
  "outputDirectory": "apps/web/dist",
  "installCommand": "pnpm install"
}
```

**Deployment Steps:**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Or use GitHub integration
git push origin main  # Auto-deploys if connected
```

### 2. **Netlify Deployment**

**netlify.toml:**
```toml
[build]
  base = "."
  command = "pnpm build:web"
  publish = "apps/web/dist"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--prefix=/dev/null"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

**Deployment Steps:**
```bash
# Install Netlify CLI
npm i -g netlify-cli

# Build and deploy
pnpm build
netlify deploy --prod --dir=apps/web/dist
```

### 3. **Docker Deployment**

**Dockerfile:**
```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder

# Install pnpm
RUN npm install -g pnpm

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json pnpm-lock.yaml pnpm-workspace.yaml ./
COPY apps/web/package.json ./apps/web/
COPY packages/*/package.json ./packages/*/

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build application
RUN pnpm build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=builder /app/apps/web/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

**nginx.conf:**
```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        # Enable gzip compression
        gzip on;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

        # Cache static assets
        location /assets/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
        }
    }
}
```

**Build and Deploy:**
```bash
# Build Docker image
docker build -t novaest-app .

# Run container
docker run -p 80:80 novaest-app

# Or use docker-compose
docker-compose up -d
```

### 4. **AWS S3 + CloudFront**

**Deploy Script (deploy-aws.sh):**
```bash
#!/bin/bash

# Build application
pnpm build

# Sync to S3
aws s3 sync apps/web/dist/ s3://your-bucket-name --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"

echo "Deployment complete!"
```

**CloudFront Configuration:**
- Origin: S3 bucket
- Default Root Object: index.html
- Error Pages: 404 → /index.html (for SPA routing)
- Caching: Cache static assets, no-cache for index.html

## 🔧 Environment Configuration

### 1. **Environment Variables**

**Development (.env.local):**
```bash
NODE_ENV=development
VITE_API_URL=http://localhost:3000
VITE_APP_VERSION=dev
VITE_ENABLE_DEVTOOLS=true
```

**Staging (.env.staging):**
```bash
NODE_ENV=production
VITE_API_URL=https://staging-api.yourdomain.com
VITE_APP_VERSION=staging
VITE_ENABLE_DEVTOOLS=false
```

**Production (.env.production):**
```bash
NODE_ENV=production
VITE_API_URL=https://api.yourdomain.com
VITE_APP_VERSION=1.0.0
VITE_ENABLE_DEVTOOLS=false
VITE_SENTRY_DSN=your-production-sentry-dsn
```

### 2. **Runtime Configuration**

**config.ts:**
```typescript
interface Config {
  apiUrl: string
  appVersion: string
  enableDevtools: boolean
  sentryDsn?: string
}

export const config: Config = {
  apiUrl: import.meta.env.VITE_API_URL || 'http://localhost:3000',
  appVersion: import.meta.env.VITE_APP_VERSION || 'dev',
  enableDevtools: import.meta.env.VITE_ENABLE_DEVTOOLS === 'true',
  sentryDsn: import.meta.env.VITE_SENTRY_DSN
}
```

## 📊 Monitoring and Analytics

### 1. **Error Tracking (Sentry)**

**Setup:**
```typescript
// apps/web/src/main.tsx
import * as Sentry from '@sentry/react'
import { config } from './config'

if (config.sentryDsn) {
  Sentry.init({
    dsn: config.sentryDsn,
    environment: import.meta.env.MODE,
    integrations: [
      new Sentry.BrowserTracing()
    ],
    tracesSampleRate: 1.0
  })
}
```

### 2. **Performance Monitoring**

**Web Vitals:**
```typescript
// apps/web/src/utils/analytics.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

export const trackWebVitals = () => {
  getCLS(console.log)
  getFID(console.log)
  getFCP(console.log)
  getLCP(console.log)
  getTTFB(console.log)
}
```

### 3. **Bundle Analysis**

**Analyze Script:**
```bash
# Add to package.json
"scripts": {
  "analyze": "pnpm build:web && npx vite-bundle-analyzer apps/web/dist"
}

# Run analysis
pnpm analyze
```

## 🔄 CI/CD Pipeline

### 1. **GitHub Actions**

**.github/workflows/deploy.yml:**
```yaml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'
      
      - name: Install pnpm
        run: npm install -g pnpm
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Run tests
        run: |
          pnpm deps:check
          pnpm check-types
          pnpm lint
          pnpm test
      
      - name: Build
        run: pnpm build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
          cache: 'pnpm'
      
      - name: Install pnpm
        run: npm install -g pnpm
      
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      
      - name: Build
        run: pnpm build
        env:
          NODE_ENV: production
          VITE_API_URL: ${{ secrets.API_URL }}
          VITE_SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

### 2. **Deployment Validation**

**Health Check Script:**
```bash
#!/bin/bash

# Wait for deployment
sleep 30

# Check if site is accessible
if curl -f -s https://yourdomain.com > /dev/null; then
    echo "✅ Deployment successful"
    exit 0
else
    echo "❌ Deployment failed"
    exit 1
fi
```

## 🛡️ Security Considerations

### 1. **Content Security Policy**

**CSP Headers:**
```html
<!-- In index.html -->
<meta http-equiv="Content-Security-Policy" 
      content="default-src 'self'; 
               script-src 'self' 'unsafe-inline'; 
               style-src 'self' 'unsafe-inline'; 
               img-src 'self' data: https:;">
```

### 2. **Environment Security**

- Never commit `.env` files
- Use platform-specific secret management
- Rotate API keys regularly
- Enable HTTPS only
- Implement proper CORS policies

## 📈 Performance Optimization

### 1. **Build Performance**
- Use Turborepo caching
- Implement incremental builds
- Optimize dependency resolution

### 2. **Runtime Performance**
- Enable gzip compression
- Use CDN for static assets
- Implement proper caching headers
- Monitor Core Web Vitals

### 3. **Bundle Optimization**
- Code splitting by routes and roles
- Tree shaking unused code
- Optimize images and assets
- Use modern JavaScript features

This deployment guide ensures your modular application is deployed efficiently and securely across various platforms while maintaining optimal performance.
